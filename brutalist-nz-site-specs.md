# Brutalist NZ Site Specifications
## Simple-Nuanced Personal Platform with Aotearoa Grounding

---

## **1. Purpose & Core Requirements**

| Goal | What it means in practice |
| ----- | ----- |
| Showcase long-form writing | Pull your Substack feed and render article cards with read-time, tags, hero image. |
| Host bite-size content | "Shortform" page for reels/TikToks, Twitter/X threads, carousels—one masonry grid, filterable by tag. |
| Teach & train | "AI Help" hub → evergreen guides, plus a "Workshops" calendar (live or on-demand). |
| Keep it *simple-nuanced* | Clean layout, micro-interactions, **no** light/dark toggle—default light theme punctuated by dark "spotlight" sections triggered on scroll. |
| Ground in Aotearoa | Subtle geographic and cultural markers woven throughout—never heavy-handed, always purposeful. |

---

## **2. Tech Stack Choices**

| Layer | Decision | Why |
| ----- | ----- | ----- |
| Framework | **Vue 3 (Composition API)** | Lightweight, reactive, mature ecosystem. |
| Build | **Vite** | Instant dev-server, lightning HMR, Tailwind friendly. |
| Styling | **Tailwind CSS v3** + `@tailwindcss/typography` | Utility first, quick theming, Prose plugin for article bodies. |
| Animations | **vueuse/motion** for micro-moves; **GSAP + ScrollTrigger** for Matariki constellation & dark-section reveals. |  |
| State / data | **Pinia** (optional) or simple `@vueuse/core` composables—enough for feed caching, modal toggles, project data. |  |
| Content | Substack RSS → `/api/articles.json` with a tiny Netlify/Vercel edge function; Markdown (or MDX via `vite-plugin-md`) for static AI guides. |  |
| Deploy | Vercel, Netlify, or Cloudflare Pages—push-to-deploy, free SSL, preview environments. |  |
| CI / CD | GitHub Actions → lint, type-check, run `vitest`, build, deploy. |  |

---

## **3. Information Architecture & Routing**

```
/
├─ / (Landing) – hero with Matariki constellation, about, latest Substack articles ↴
├─ /articles      – full article feed, category filters
├─ /article/[slug] – article detail (hydrated RSS → HTML)
├─ /shortform     – grid of short-media embeds (YouTube, IG, X)
├─ /ai-help       – index of AI primers with workshops; /ai-help/[slug] for each guide
├─ /projects      – current & past projects, portfolio showcase ↴
│   └─ /projects/[id] – project details, links, case studies
└─ /contact       – DM links, email, Ko-fi "shout me a coffee" button
```

---

## **4. Visual & Interaction Language**

### Core Palette & Typography

| Element | Treatment |
| ----- | ----- |
| **Color System** | CSS custom properties for flexibility:<br>`--color-base: #f5f7fa` (near-white)<br>`--color-text: #1b1f23`<br>`--color-fern: #0d4225` (deep Aotearoa fern)<br>`--color-kawakawa: #1a5f3f` (lighter tint)<br>`--color-charcoal: #111418` (dark sections)<br>`--color-mist: #e8eaed` (subtle borders) |
| **Typography** | Headings: `Montserrat` 600<br>Body: `Inter` 400<br>**Tightened scale**: h1 `clamp(1.75rem, 4vw, 2.25rem)`, h2 `1.75rem`, h3 `1.5rem`, body `1rem`<br>Line heights: headings `1.2`, body `1.6` |
| **Grid System** | Strict 12-column grid, 20px gutters, max-width 1200px<br>CSS Grid with named template areas for semantic layout |
| **Spacing Scale** | Based on 8px: `--space-1: 0.5rem` through `--space-12: 6rem` |

### Micro-Interactions & Movement

| Element | Behavior |
| ----- | ----- |
| **Card hover** | `transform: translateY(-4px)` + subtle shadow bloom |
| **Icon interactions** | 2-3° rotation on tap/click |
| **Initial load** | Elements fade in: `opacity: 0 → 1` over 300ms with 50ms stagger |
| **Scroll parallax** | Background images move at 0.9x scroll speed |
| **Matariki constellation** | SVG line drawing that morphs and recolors through scroll zones |

### The Matariki Element

A delicate SVG constellation of nine stars (Matariki cluster) serves as a persistent visual thread:
- **Initial state**: Positioned top-right of hero, thin lines in `--color-kawakawa`
- **Scroll behavior**: Gentle rotation and translation following scroll position
- **Dark section entry**: Lines transition to soft white (`rgba(255,255,255,0.7)`)
- **Interactive moments**: Individual stars pulse subtly when near interactive elements
- **Implementation**: Single SVG with GSAP timeline, total file size < 5KB

---

## **5. Geographic & Cultural Grounding**

### Subtle Aotearoa Markers

| Element | Implementation |
| ----- | ----- |
| **Time displays** | Always show NZST/NZDT with subtle "Auckland" label |
| **Weather widget** | Optional: Minimal current Auckland conditions in footer |
| **Microcopy tone** | Informal but clear, occasional "kia ora" in welcome messages |
| **404 page** | "You're a bit lost, eh?" with directional compass pointing to NZ |
| **Loading states** | "Just a tick..." instead of generic "Loading..." |

### Visual Language

| Aspect | Direction |
| ----- | ----- |
| **Photography** | When used: Natural light, native flora textures, coastal elements<br>Always desaturated slightly to maintain brutal aesthetic |
| **Patterns** | Subtle koru-inspired curves in section dividers (SVG, not images) |
| **Icons** | Custom icon set with subtle NZ influences (fern frond for "growth", etc.) |

---

## **6. Smart CSS Techniques**

```css
/* Fluid typography with clamp() */
h1 { font-size: clamp(1.75rem, 4vw, 2.25rem); }

/* Container queries for component responsiveness */
.article-card {
  container-type: inline-size;
}

@container (min-width: 400px) {
  .article-card__content { /* ... */ }
}

/* CSS Grid template areas for readable layouts */
.page-layout {
  display: grid;
  grid-template-areas:
    "header header header"
    "nav content aside"
    "footer footer footer";
}

/* Blur-up image loading */
.lazy-image {
  filter: blur(5px);
  transition: filter 0.3s ease-out;
}
.lazy-image.loaded {
  filter: blur(0);
}
```

---

## **7. Data Flow & Integrations**

### Content Pipeline

1. **Substack Integration**
   - Edge function fetches RSS every 10 min
   - Parses and caches in `/api/articles.json`
   - Extracts hero images, applies subtle desaturation filter
   - Vue composable `useArticles()` with built-in error states

2. **Media Embeds**
   - Lazy-load on `IntersectionObserver` (threshold: 0.1)
   - Placeholder shows blurred thumbnail during load
   - Aspect ratios preserved to prevent layout shift

3. **Project System**
   - Static project data with dynamic content loading
   - Workshop functionality moved to AI Help section

---

## **8. Enhanced Directory Structure**

```
src/
├─ assets/
│   ├─ images/
│   ├─ icons/          # Custom NZ-influenced icon set
│   └─ svg/
│       └─ matariki.svg
├─ components/
│   ├─ cards/
│   │   ├─ ArticleCard.vue
│   │   └─ MediaCard.vue
│   ├─ layout/
│   │   ├─ AppHeader.vue
│   │   ├─ AppFooter.vue
│   │   └─ MatarikiConstellation.vue
│   ├─ sections/
│   │   ├─ HeroSection.vue
│   │   └─ DarkSpotlight.vue
│   └─ ui/
│       ├─ LazyImage.vue
│       └─ TimeDisplay.vue
├─ composables/
│   ├─ useArticles.ts
│   ├─ useMatariki.ts    # Constellation animation logic
│   ├─ useScrollReveal.ts
│   └─ useNZTime.ts      # Auckland timezone helper
├─ styles/
│   ├─ tailwind.css
│   └─ critical.css      # Inline critical CSS for faster FCP
└─ content/
    ├─ projects.yaml
    └─ shortform/
```

---

## **9. Performance & Accessibility**

| Aspect | Implementation |
| ----- | ----- |
| **Core Web Vitals** | LCP < 2.5s, FID < 100ms, CLS < 0.1 |
| **Bundle strategy** | Route-based code splitting, < 150KB initial JS |
| **Accessibility** | WCAG 2.1 AA compliant, 4.5:1 contrast minimum<br>`prefers-reduced-motion`: disables parallax & Matariki movement<br>Semantic HTML, proper ARIA labels |
| **SEO** | Auto-generated `sitemap.xml`, structured data for articles<br>Social cards with Matariki watermark |

---

## **10. Progressive Enhancement Roadmap**

### Phase 1 (Launch)
- Static Matariki constellation with basic scroll response
- Static project showcase with dynamic content
- Workshop functionality integrated into AI Help section
- Basic analytics (privacy-focused, maybe Plausible)

### Phase 2 (3 months)
- Matariki constellation responds to time of day/season
- Workshop booking system via ConvertKit integration
- Search functionality with Algolia

### Phase 3 (6 months)
- Advanced project showcase with case studies
- Comments on articles (considering Giscus)
- PWA capabilities for offline reading

---

## **11. Development Principles**

1. **Every element serves a purpose** - no decoration without function
2. **Performance is a feature** - target 100 Lighthouse score
3. **Accessibility is non-negotiable** - test with screen readers
4. **Cultural elements enhance, never appropriate** - subtle and respectful
5. **Code clarity over cleverness** - future you will thank present you

This specification creates a foundation that's both minimal and sophisticated, grounded in place while universally accessible.