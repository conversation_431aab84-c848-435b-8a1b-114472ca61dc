<script setup lang="ts">
import { onMounted, ref } from 'vue'
import BaseScrollSection from './BaseScrollSection.vue'
import { gsap } from 'gsap'

const sectionRef = ref<HTMLElement>()

// AI Help specific animation configuration
const aiHelpAnimationConfig = {
  skeletonToEntering: {
    duration: 1.2,
    ease: "power2.out"
  },
  enteringToLoaded: {
    duration: 1.5,
    ease: "power3.inOut"
  },
  customAnimations: {
    darkExpansion: {
      duration: 2,
      ease: "power2.inOut"
    },
    waveFlow: {
      duration: 4,
      ease: "sine.inOut",
      repeat: -1,
      yoyo: true
    }
  }
}

// Sample AI help content
const guides = [
  {
    id: 1,
    icon: "📋",
    title: "C-S-T & P-I-T Framework",
    description: "Master AI prompting with our structured approach. Context, Skills, Tasks, Purpose, Ingredients, Targets.",
    type: "Framework"
  },
  {
    id: 2,
    icon: "🎯",
    title: "ROI Calculator for AI Tools",
    description: "Calculate real business value from AI implementations. No fluff, just numbers that matter.",
    type: "Tool"
  },
  {
    id: 3,
    icon: "🧠",
    title: "AI Ethics Checklist",
    description: "Ensure responsible AI adoption with tikanga-inspired ethical guidelines.",
    type: "Guide"
  }
]

const handleSectionActive = () => {
  // Trigger dark expansion animation
  const darkFrame = document.querySelector('.dark-expansion-frame')
  if (darkFrame) {
    gsap.fromTo(darkFrame, 
      { 
        width: '200px',
        height: '200px',
        borderRadius: '50%',
        opacity: 0.3
      },
      {
        width: '100vw',
        height: '100vh',
        borderRadius: '0%',
        opacity: 1,
        duration: 2,
        ease: "power2.inOut"
      }
    )
  }

  // Animate wave patterns
  const waveLines = document.querySelectorAll('.wave-line')
  waveLines.forEach((line, index) => {
    gsap.fromTo(line,
      { strokeDashoffset: (line as SVGPathElement).getTotalLength() },
      {
        strokeDashoffset: 0,
        duration: 3,
        delay: index * 0.3,
        ease: "power2.out"
      }
    )
  })
}

const handleStateChange = (state: string, progress: number) => {
  // Handle global theme changes for this section
  const body = document.body
  
  if (state === 'entering' || state === 'loaded') {
    body.classList.add('dark-theme-active')
  } else {
    body.classList.remove('dark-theme-active')
  }
}

onMounted(() => {
  // Initialize wave pattern animations
  const waveLines = document.querySelectorAll('.wave-line')
  waveLines.forEach(line => {
    const length = (line as SVGPathElement).getTotalLength()
    gsap.set(line, {
      strokeDasharray: length,
      strokeDashoffset: length
    })
  })
})
</script>

<template>
  <BaseScrollSection
    ref="sectionRef"
    section-id="ai-help"
    star-name="waita"
    theme-title="Waitā"
    theme-subtitle="Salt Water • Vast Knowledge • Ocean of Wisdom"
    main-title="AI Help & Guides"
    background-pattern="deep-waves"
    class-name="ai-help-section"
    :dark-theme="true"
    @section-active="handleSectionActive"
    @state-change="handleStateChange"
  >
    <!-- Skeleton Content with Dark Frame -->
    <template #skeleton>
      <div class="ai-help-skeleton">
        <!-- Dark expansion frame -->
        <div class="dark-expansion-frame"></div>
        
        <div class="skeleton-guides">
          <div class="skeleton-guide" v-for="n in 3" :key="n">
            <div class="skeleton-icon"></div>
            <div class="skeleton-guide-content">
              <div class="skeleton-guide-title"></div>
              <div class="skeleton-guide-description"></div>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- Header Content -->
    <template #header>
      <p class="section-subtitle" data-gsap="section-subtitle">
        Practical frameworks and upcoming workshops
      </p>
      <RouterLink to="/ai-help" class="section-cta" data-gsap="section-cta">
        Explore AI Help →
      </RouterLink>
    </template>

    <!-- Main Content -->
    <template #content>
      <div class="ai-help-content" data-gsap="ai-help-content">
        <div class="guides-grid" data-gsap="guides-grid">
          <div 
            v-for="guide in guides" 
            :key="guide.id"
            class="guide-card"
            data-gsap="guide-card"
          >
            <div class="guide-icon">{{ guide.icon }}</div>
            <div class="guide-content">
              <div class="guide-type">{{ guide.type }}</div>
              <h3 class="guide-title">{{ guide.title }}</h3>
              <p class="guide-description">{{ guide.description }}</p>
              <button class="guide-cta">Learn More →</button>
            </div>
          </div>
        </div>

        <div class="workshops-preview" data-gsap="workshops-preview">
          <div class="workshop-announcement">
            <h3 class="workshop-title">Upcoming Workshops</h3>
            <p class="workshop-description">
              Interactive sessions combining AI frameworks with tikanga principles. 
              Building practical skills for respectful innovation.
            </p>
            <div class="workshop-status">
              <span class="status-badge">Coming Soon</span>
              <button class="notify-button">Get Notified</button>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- Background Pattern - Deep Waves -->
    <template #background-pattern>
      <div class="pattern-deep-waves">
        <svg class="deep-waves-svg" viewBox="0 0 1200 800" preserveAspectRatio="xMidYMid slice">
          <defs>
            <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.1" />
              <stop offset="50%" style="stop-color:#4A90E2;stop-opacity:0.2" />
              <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.05" />
            </linearGradient>
            <radialGradient id="depthGradient" cx="50%" cy="50%" r="50%">
              <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:0.3" />
              <stop offset="100%" style="stop-color:#1a1a1a;stop-opacity:0.1" />
            </radialGradient>
          </defs>
          
          <!-- Deep ocean wave patterns -->
          <path 
            class="wave-line"
            d="M0,600 Q300,500 600,550 T1200,500"
            stroke="url(#waveGradient)"
            stroke-width="3"
            fill="none"
            opacity="0.8"
          />
          <path 
            class="wave-line"
            d="M0,650 Q400,550 800,600 T1200,550"
            stroke="url(#waveGradient)"
            stroke-width="2"
            fill="none"
            opacity="0.6"
          />
          <path 
            class="wave-line"
            d="M0,700 Q200,600 400,650 T800,600 Q1000,550 1200,600"
            stroke="url(#waveGradient)"
            stroke-width="1.5"
            fill="none"
            opacity="0.4"
          />
          
          <!-- Depth indicators -->
          <circle cx="300" cy="400" r="50" fill="url(#depthGradient)" opacity="0.3" />
          <circle cx="800" cy="300" r="30" fill="url(#depthGradient)" opacity="0.2" />
          <circle cx="1000" cy="500" r="40" fill="url(#depthGradient)" opacity="0.25" />
        </svg>
      </div>
    </template>
  </BaseScrollSection>
</template>

<style scoped>
.ai-help-section {
  position: relative;
  background: var(--color-charcoal);
  color: white;
  overflow: hidden;
}

/* Dark Expansion Frame */
.dark-expansion-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, var(--color-charcoal) 0%, #0a0a0a 100%);
  border-radius: 50%;
  opacity: 0.3;
  z-index: 1;
}

/* Skeleton Styles - Dark Theme */
.ai-help-skeleton {
  position: relative;
}

.skeleton-guides {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
  max-width: 1200px;
  margin: 0 auto;
}

.skeleton-guide {
  display: flex;
  gap: var(--space-4);
  padding: var(--space-6);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
}

.skeleton-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(90deg, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 75%);
  background-size: 200% 100%;
  animation: shimmerDark 2s infinite;
  border-radius: 12px;
  flex-shrink: 0;
}

.skeleton-guide-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.skeleton-guide-title {
  height: 1.5rem;
  width: 70%;
  background: linear-gradient(90deg, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 75%);
  background-size: 200% 100%;
  animation: shimmerDark 2s infinite 0.3s;
  border-radius: 6px;
}

.skeleton-guide-description {
  height: 3rem;
  width: 90%;
  background: linear-gradient(90deg, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 75%);
  background-size: 200% 100%;
  animation: shimmerDark 2s infinite 0.6s;
  border-radius: 6px;
}

/* Main Content Styles */
.section-subtitle {
  font-size: var(--text-lg);
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--space-4);
}

.section-cta {
  display: inline-flex;
  align-items: center;
  color: #4A90E2;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.section-cta:hover {
  color: #66A3E8;
  transform: translateX(4px);
}

.guides-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.guide-card {
  display: flex;
  gap: var(--space-4);
  padding: var(--space-6);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.guide-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(74, 144, 226, 0.3);
  transform: translateY(-4px);
}

.guide-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(74, 144, 226, 0.2);
  border-radius: 12px;
  flex-shrink: 0;
}

.guide-content {
  flex: 1;
}

.guide-type {
  font-size: var(--text-sm);
  color: #4A90E2;
  font-weight: 500;
  margin-bottom: var(--space-2);
}

.guide-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: white;
  margin-bottom: var(--space-3);
  line-height: 1.3;
}

.guide-description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  margin-bottom: var(--space-4);
}

.guide-cta {
  background: none;
  border: none;
  color: #4A90E2;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  font-size: var(--text-base);
}

.guide-cta:hover {
  color: #66A3E8;
  transform: translateX(4px);
}

/* Workshops Preview */
.workshops-preview {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: var(--space-8);
  text-align: center;
}

.workshop-title {
  font-size: var(--text-2xl);
  font-weight: 600;
  color: white;
  margin-bottom: var(--space-4);
}

.workshop-description {
  font-size: var(--text-lg);
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: var(--space-6);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.workshop-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-4);
}

.status-badge {
  background: rgba(74, 144, 226, 0.2);
  color: #4A90E2;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: var(--text-sm);
  font-weight: 500;
}

.notify-button {
  background: #4A90E2;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.notify-button:hover {
  background: #66A3E8;
  transform: translateY(-2px);
}

/* Background Pattern */
.pattern-deep-waves {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.deep-waves-svg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* Global dark theme class for body */
:global(.dark-theme-active) {
  background-color: var(--color-charcoal);
  transition: background-color 0.8s ease;
}

/* Dark shimmer animation */
@keyframes shimmerDark {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .guides-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .guide-card {
    flex-direction: column;
    text-align: center;
  }
  
  .workshop-status {
    flex-direction: column;
    gap: var(--space-3);
  }
}
</style>
