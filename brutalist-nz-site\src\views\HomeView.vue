<script setup lang="ts">
import { onMounted } from 'vue'
import MatarikiConstellation from '../components/MatarikiConstellation.vue'
import ArticlesSection from '../components/sections/ArticlesSection.vue'
import AIHelpSection from '../components/sections/AIHelpSection.vue'
import { useMatarikiAnimation } from '../composables/useMatarikiAnimation'

const { initializeConstellation } = useMatarikiAnimation()

onMounted(() => {
  // Initialize Matariki constellation animations
  setTimeout(() => {
    initializeConstellation()
  }, 100)
})

// Handle section state changes for constellation updates
const handleSectionActive = (sectionId: string) => {
  console.log(`Section ${sectionId} is now active`)
  // This could trigger constellation star highlighting
}
</script>

<template>
  <div class="home-page">
    <!-- Fixed Matariki Constellation for entire page -->
    <MatarikiConstellation />

    <!-- Hero Section -->
    <section class="hero-section" data-scroll-section="hero">
      <div class="site-container hero-container">
        <div class="hero-content" data-gsap="hero-content">
          <h1 class="hero-title" data-gsap="hero-title">
            Grounded Insight
            <span class="hero-subtitle" data-gsap="hero-subtitle">Global tech. Kiwi heart.</span>
          </h1>
          <p class="hero-description" data-gsap="hero-description">
            Long-form thinking and real-world AI advice, exploring how innovation and tikanga shape each other
          </p>
          <div class="hero-actions" data-gsap="hero-actions">
            <RouterLink to="/articles" class="cta-button primary">
              Read Articles
            </RouterLink>
            <RouterLink to="/ai-help" class="cta-button secondary">
              AI Help
            </RouterLink>
          </div>
        </div>
      </div>
    </section>

    <!-- Articles Preview Section - New Component -->
    <ArticlesSection @section-active="handleSectionActive" />

    <!-- Shortform Preview Section -->
    <section class="shortform-preview-section section-skeleton" data-scroll-section="shortform">
      <div class="site-container">
        <div class="section-header" data-gsap="section-header">
          <h2 class="section-title">Quick Takes & Media</h2>
          <p class="section-subtitle">Bite-sized insights from social platforms</p>
          <RouterLink to="/shortform" class="section-cta">View All Media →</RouterLink>
        </div>
        <div class="shortform-grid" data-gsap="shortform-grid">
          <div class="media-card youtube-card" data-gsap="media-card">
            <div class="media-thumbnail">
              <div class="play-button">▶</div>
              <div class="media-duration">3:42</div>
            </div>
            <div class="media-content">
              <h4 class="media-title">AI Tool Speed Test: Claude vs ChatGPT</h4>
              <p class="media-platform">YouTube</p>
            </div>
          </div>

          <div class="media-card twitter-card" data-gsap="media-card">
            <div class="media-content">
              <div class="tweet-header">
                <span class="tweet-author">@framecheck_nz</span>
                <span class="tweet-time">2h</span>
              </div>
              <p class="tweet-content">
                "Just tested 5 AI writing tools on the same brief. The winner might surprise you... 🧵"
              </p>
              <div class="tweet-stats">
                <span>12 replies</span>
                <span>34 retweets</span>
                <span>89 likes</span>
              </div>
            </div>
          </div>

          <div class="media-card instagram-card" data-gsap="media-card">
            <div class="media-thumbnail">
              <div class="instagram-icon">📸</div>
            </div>
            <div class="media-content">
              <h4 class="media-title">Behind the scenes: AI tool testing setup</h4>
              <p class="media-platform">Instagram</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- AI Help Preview Section - New Component -->
    <AIHelpSection @section-active="handleSectionActive" />

    <!-- Projects Preview Section -->
    <section class="projects-preview-section section-skeleton" data-scroll-section="projects">
      <div class="site-container">
        <div class="section-header" data-gsap="section-header">
          <h2 class="section-title">Current Projects</h2>
          <p class="section-subtitle">Building tools and frameworks for better AI adoption</p>
          <RouterLink to="/projects" class="section-cta">View All Projects →</RouterLink>
        </div>
        <div class="projects-grid" data-gsap="projects-grid">
          <div class="project-card featured" data-gsap="project-card">
            <div class="project-icon">📰</div>
            <div class="project-content">
              <h3 class="project-title">Frame Check Newsletter</h3>
              <p class="project-description">
                Honest ROI analysis of AI tools for busy professionals, incorporating New Zealand values and practical wisdom.
              </p>
              <div class="project-meta">
                <span class="project-type">Newsletter</span>
                <span class="project-status active">Active</span>
              </div>
              <div class="project-stats">
                <span class="stat">Weekly</span>
                <span class="stat">500+ subscribers</span>
              </div>
            </div>
          </div>

          <div class="project-card" data-gsap="project-card">
            <div class="project-icon">📋</div>
            <div class="project-content">
              <h3 class="project-title">C-S-T & P-I-T Framework</h3>
              <p class="project-description">
                A structured approach to AI prompting that ensures consistent, high-quality results.
              </p>
              <div class="project-meta">
                <span class="project-type">Framework</span>
                <span class="project-status complete">Complete</span>
              </div>
              <div class="project-stats">
                <span class="stat">Open Source</span>
                <span class="stat">1000+ downloads</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Preview Section -->
    <section class="contact-preview-section section-skeleton" data-scroll-section="contact">
      <div class="site-container">
        <div class="contact-content" data-gsap="contact-content">
          <div class="contact-header">
            <h2 class="section-title">Let's Connect</h2>
            <p class="section-subtitle">
              Questions about AI tools? Want to collaborate? Or just fancy a chat about tech and tikanga?
            </p>
          </div>
          <div class="contact-options" data-gsap="contact-options">
            <div class="contact-option" data-gsap="contact-option">
              <div class="contact-icon">📧</div>
              <h3 class="contact-method">Email</h3>
              <p class="contact-description">For detailed discussions and collaboration</p>
              <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
            </div>

            <div class="contact-option" data-gsap="contact-option">
              <div class="contact-icon">🐦</div>
              <h3 class="contact-method">Twitter</h3>
              <p class="contact-description">Quick questions and daily AI insights</p>
              <a href="#" class="contact-link">@framecheck_nz</a>
            </div>

            <div class="contact-option" data-gsap="contact-option">
              <div class="contact-icon">☕</div>
              <h3 class="contact-method">Ko-fi</h3>
              <p class="contact-description">Support the work with a coffee</p>
              <a href="#" class="contact-link">Buy me a coffee</a>
            </div>
          </div>
          <div class="contact-cta" data-gsap="contact-cta">
            <RouterLink to="/contact" class="cta-button primary">
              Get In Touch
            </RouterLink>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
.home-page {
  min-height: 100vh;
}

/* Progressive Reveal System */
.section-skeleton {
  opacity: 0.3;
  filter: blur(1px);
}

.section-skeleton .section-title {
  background: var(--color-border);
  color: transparent;
  border-radius: 4px;
}

.section-skeleton .section-subtitle {
  background: var(--color-border);
  color: transparent;
  border-radius: 4px;
  width: 70%;
}

.section-skeleton .article-card,
.section-skeleton .shortform-card,
.section-skeleton .project-card {
  background: var(--color-surface);
  border: 2px dashed var(--color-border);
}

.section-skeleton .article-content,
.section-skeleton .shortform-content,
.section-skeleton .project-content {
  opacity: 0.2;
}

.section-revealed {
  opacity: 1;
  filter: blur(0);
  transition: all 0.8s ease-out;
}

.section-revealed .section-title,
.section-revealed .section-subtitle {
  background: transparent;
  color: inherit;
  transition: all 0.6s ease-out 0.2s;
}

.section-revealed .article-card,
.section-revealed .shortform-card,
.section-revealed .project-card {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  transition: all 0.6s ease-out 0.4s;
}

.section-revealed .article-content,
.section-revealed .shortform-content,
.section-revealed .project-content {
  opacity: 1;
  transition: opacity 0.6s ease-out 0.6s;
}

/* Section Background Transitions */
.articles-preview-section.section-active {
  background: linear-gradient(135deg, var(--color-base) 0%, rgba(240, 248, 255, 0.3) 100%);
  transition: background 0.8s ease-out;
}

.shortform-preview-section.section-active {
  background: linear-gradient(135deg, var(--color-base) 0%, rgba(248, 250, 252, 0.3) 100%);
  transition: background 0.8s ease-out;
}

.ai-help-preview-section.section-active {
  background: linear-gradient(135deg, var(--color-base) 0%, rgba(245, 247, 250, 0.3) 100%);
  transition: background 0.8s ease-out;
}

.projects-preview-section.section-active {
  background: linear-gradient(135deg, var(--color-base) 0%, rgba(250, 252, 254, 0.3) 100%);
  transition: background 0.8s ease-out;
}

.contact-preview-section.section-active {
  background: linear-gradient(135deg, var(--color-base) 0%, rgba(252, 254, 255, 0.3) 100%);
  transition: background 0.8s ease-out;
}

/* Hero Section */
.hero-section {
  padding: var(--space-12) 0 var(--space-8);
  background: linear-gradient(135deg, var(--color-base) 0%, rgba(245, 247, 250, 0.8) 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-content {
  max-width: 800px;
  text-align: center;
  margin: 0 auto;
}

.hero-title {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.hero-subtitle {
  display: block;
  font-size: 0.7em;
  color: var(--color-kawakawa);
  font-style: italic;
  margin-top: var(--space-2);
}

.hero-description {
  font-size: 1.25rem;
  line-height: 1.6;
  color: var(--color-text);
  margin-bottom: var(--space-6);
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  display: inline-block;
  padding: var(--space-3) var(--space-6);
  text-decoration: none;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.cta-button.primary {
  background-color: var(--color-fern);
  color: white;
  border-color: var(--color-fern);
}

.cta-button.primary:hover {
  background-color: var(--color-kawakawa);
  border-color: var(--color-kawakawa);
  transform: translateY(-2px);
}

.cta-button.secondary {
  background-color: transparent;
  color: var(--color-fern);
  border-color: var(--color-fern);
}

.cta-button.secondary:hover {
  background-color: var(--color-fern);
  color: white;
  transform: translateY(-2px);
}

/* Enhanced Section Transitions */
.section-active {
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Smooth text color transitions for AI Help section */
.ai-help-preview-section .section-subtitle,
.ai-help-preview-section .guide-description,
.ai-help-preview-section .workshops-note {
  transition: color 1.2s ease;
}

.ai-help-preview-section.section-active .section-subtitle,
.ai-help-preview-section.section-active .guide-description,
.ai-help-preview-section.section-active .workshops-note {
  color: rgba(255, 255, 255, 0.9);
}

/* Section Styles with Natural Patterns */
.articles-preview-section,
.shortform-preview-section,
.ai-help-preview-section,
.projects-preview-section,
.contact-preview-section {
  padding: var(--space-12) 0;
  position: relative;
  transition: all 0.8s ease;
}

/* Hero Section - Dawn Light Radiating */
.hero-section {
  background:
    radial-gradient(circle at 30% 20%, rgba(76, 175, 80, 0.05) 0%, transparent 50%),
    linear-gradient(135deg, var(--color-base) 0%, rgba(245, 247, 250, 0.8) 100%);
}

.hero-section.section-active {
  background:
    radial-gradient(circle at 30% 20%, rgba(76, 175, 80, 0.1) 0%, transparent 60%),
    linear-gradient(135deg, var(--color-base) 0%, rgba(245, 247, 250, 0.9) 100%);
}

/* Articles Section - Organic Tree Branches */
.articles-preview-section {
  background:
    radial-gradient(ellipse at 80% 20%, rgba(76, 175, 80, 0.03) 0%, transparent 50%),
    var(--color-base);
}

.articles-preview-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(ellipse 200px 100px at 20% 30%, rgba(76, 175, 80, 0.02) 0%, transparent 70%),
    radial-gradient(ellipse 150px 80px at 80% 70%, rgba(76, 175, 80, 0.02) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.8s ease;
  pointer-events: none;
}

.articles-preview-section.section-active::before {
  opacity: 1;
}

/* Shortform Section - Water Droplets */
.shortform-preview-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.shortform-preview-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle 30px at 25% 25%, rgba(74, 144, 226, 0.08) 0%, transparent 70%),
    radial-gradient(circle 20px at 75% 40%, rgba(74, 144, 226, 0.06) 0%, transparent 70%),
    radial-gradient(circle 25px at 40% 80%, rgba(74, 144, 226, 0.07) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.8s ease;
  pointer-events: none;
}

.shortform-preview-section.section-active::before {
  opacity: 1;
}

/* AI Help Section - Ocean Depths (Light to Dark Priority) */
.ai-help-preview-section {
  background:
    linear-gradient(135deg, var(--color-base) 0%, #f1f3f4 50%, #e2e8f0 100%);
  transition: all 1.2s ease;
}

.ai-help-preview-section.section-active {
  background:
    linear-gradient(135deg, #f1f3f4 0%, #e2e8f0 30%, #4a5568 70%, #2d3748 100%);
  color: white;
}

.ai-help-preview-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(ellipse 300px 150px at 30% 20%, rgba(44, 82, 130, 0.1) 0%, transparent 70%),
    radial-gradient(ellipse 200px 100px at 80% 80%, rgba(44, 82, 130, 0.08) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 1.2s ease;
  pointer-events: none;
}

.ai-help-preview-section.section-active::before {
  opacity: 1;
}

.ai-help-preview-section.section-active .section-title,
.ai-help-preview-section.section-active .section-subtitle {
  color: white;
  transition: color 1.2s ease;
}

/* Projects Section - Growth Spirals */
.projects-preview-section {
  background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
}

.projects-preview-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(ellipse 150px 300px at 20% 50%, rgba(76, 175, 80, 0.04) 0%, transparent 70%),
    radial-gradient(ellipse 100px 200px at 90% 30%, rgba(76, 175, 80, 0.03) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.8s ease;
  pointer-events: none;
}

.projects-preview-section.section-active::before {
  opacity: 1;
}

/* Contact Section - Wind Patterns */
.contact-preview-section {
  background: linear-gradient(135deg, var(--color-kawakawa) 0%, var(--color-fern) 100%);
  color: white;
}

.contact-preview-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(ellipse 200px 80px at 40% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 70%),
    radial-gradient(ellipse 150px 60px at 80% 60%, rgba(255, 255, 255, 0.03) 0%, transparent 70%),
    radial-gradient(ellipse 180px 70px at 20% 80%, rgba(255, 255, 255, 0.04) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.8s ease;
  pointer-events: none;
}

.contact-preview-section.section-active::before {
  opacity: 1;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.section-title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-3);
}

.contact-preview-section .section-title {
  color: white;
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--color-text-muted);
  margin-bottom: var(--space-4);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.contact-preview-section .section-subtitle {
  color: rgba(255, 255, 255, 0.9);
}

.section-cta {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--color-kawakawa);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.section-cta:hover {
  color: var(--color-fern);
  text-decoration: underline;
}

/* Articles Grid */
.articles-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  max-width: 1200px;
  margin: 0 auto;
}

.article-card {
  background: white;
  border: 1px solid var(--color-mist);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.article-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--color-kawakawa);
}

.article-image-placeholder {
  height: 200px;
  background: linear-gradient(135deg, var(--color-kawakawa) 0%, var(--color-fern) 100%);
  position: relative;
}

.article-image-placeholder::after {
  content: '📰';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 3rem;
  opacity: 0.7;
}

.article-content {
  padding: var(--space-5);
}

.article-meta {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
  font-size: 0.875rem;
}

.article-date {
  color: var(--color-text-muted);
}

.article-category {
  background: var(--color-kawakawa);
  color: white;
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.75rem;
  text-transform: uppercase;
}

.article-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-3);
  line-height: 1.3;
}

.article-excerpt {
  color: var(--color-text-muted);
  line-height: 1.6;
  margin-bottom: var(--space-4);
}

.article-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: var(--color-text-muted);
  border-top: 1px solid var(--color-mist);
  padding-top: var(--space-3);
}

.read-time,
.engagement {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

/* Shortform Media Section */
.shortform-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  max-width: 1000px;
  margin: 0 auto;
}

.media-card {
  background: white;
  border: 1px solid var(--color-mist);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.media-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.media-thumbnail {
  height: 180px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.youtube-card .media-thumbnail {
  background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
}

.instagram-card .media-thumbnail {
  background: linear-gradient(135deg, #e4405f 0%, #833ab4 50%, #fcb045 100%);
}

.play-button {
  color: white;
  font-size: 3rem;
  opacity: 0.9;
}

.instagram-icon {
  font-size: 3rem;
  opacity: 0.9;
}

.media-duration {
  position: absolute;
  bottom: var(--space-2);
  right: var(--space-2);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.media-content {
  padding: var(--space-4);
}

.media-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-2);
  line-height: 1.3;
}

.media-platform {
  font-size: 0.875rem;
  color: var(--color-text-muted);
  font-weight: 500;
}

.twitter-card .media-content {
  padding: var(--space-5);
}

.tweet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
  font-size: 0.875rem;
}

.tweet-author {
  color: var(--color-kawakawa);
  font-weight: 600;
}

.tweet-time {
  color: var(--color-text-muted);
}

.tweet-content {
  color: var(--color-text);
  line-height: 1.5;
  margin-bottom: var(--space-4);
}

.tweet-stats {
  display: flex;
  gap: var(--space-4);
  font-size: 0.75rem;
  color: var(--color-text-muted);
}

/* AI Help Section */
.ai-help-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
  max-width: 1000px;
  margin: 0 auto;
}

.guide-showcase {
  background: white;
  border: 1px solid var(--color-mist);
  border-radius: 12px;
  padding: var(--space-6);
  text-align: center;
}

.guide-icon {
  font-size: 3rem;
  margin-bottom: var(--space-4);
}

.guide-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-3);
}

.guide-description {
  color: var(--color-text-muted);
  line-height: 1.6;
  margin-bottom: var(--space-4);
}

.guide-features {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  align-items: center;
}

.feature {
  color: var(--color-kawakawa);
  font-weight: 500;
  font-size: 0.875rem;
}

.workshops-preview {
  background: white;
  border: 1px solid var(--color-mist);
  border-radius: 12px;
  padding: var(--space-6);
}

.workshops-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-4);
  text-align: center;
}

.workshop-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.workshop-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  background: var(--color-base);
  border-radius: 8px;
}

.workshop-name {
  font-weight: 500;
  color: var(--color-text);
}

.workshop-status {
  font-size: 0.75rem;
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
  background: var(--color-text-muted);
  color: white;
  text-transform: uppercase;
  font-weight: 500;
}

.workshops-note {
  text-align: center;
  font-size: 0.875rem;
  color: var(--color-text-muted);
  font-style: italic;
}

/* Projects Section */
.projects-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  max-width: 1000px;
  margin: 0 auto;
}

.project-card {
  background: white;
  border: 1px solid var(--color-mist);
  border-radius: 12px;
  padding: var(--space-6);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.project-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border-color: var(--color-kawakawa);
}

.project-card.featured {
  border-color: var(--color-kawakawa);
  border-width: 2px;
}

.project-icon {
  font-size: 2.5rem;
  margin-bottom: var(--space-4);
}

.project-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-3);
}

.project-description {
  color: var(--color-text-muted);
  line-height: 1.6;
  margin-bottom: var(--space-4);
}

.project-meta {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.project-type {
  background: var(--color-mist);
  color: var(--color-text);
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.project-status {
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  color: white;
}

.project-status.active {
  background: var(--color-kawakawa);
}

.project-status.complete {
  background: var(--color-fern);
}

.project-stats {
  display: flex;
  gap: var(--space-4);
  font-size: 0.875rem;
  color: var(--color-text-muted);
}

.stat {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

/* Contact Section */
.contact-content {
  max-width: 1000px;
  margin: 0 auto;
  text-align: center;
}

.contact-header {
  margin-bottom: var(--space-8);
}

.contact-options {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.contact-option {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: var(--space-6);
  transition: all 0.3s ease;
}

.contact-option:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.contact-icon {
  font-size: 2.5rem;
  margin-bottom: var(--space-3);
}

.contact-method {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin-bottom: var(--space-2);
}

.contact-description {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--space-3);
  line-height: 1.5;
}

.contact-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  transition: border-color 0.2s ease;
}

.contact-link:hover {
  border-color: white;
}



/* Responsive Design */
@media (min-width: 768px) {
  .articles-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .shortform-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .ai-help-content {
    grid-template-columns: 1fr 1fr;
  }

  .projects-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .contact-options {
    grid-template-columns: repeat(2, 1fr);
  }

  .guide-features {
    flex-direction: row;
    justify-content: center;
  }
}

@media (min-width: 1024px) {
  .articles-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .shortform-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .contact-options {
    grid-template-columns: repeat(3, 1fr);
  }

  /* Reduce pattern intensity on smaller screens */
  .articles-preview-section::before,
  .shortform-preview-section::before,
  .ai-help-preview-section::before,
  .projects-preview-section::before,
  .contact-preview-section::before {
    opacity: 0.7;
  }

  .articles-preview-section.section-active::before,
  .shortform-preview-section.section-active::before,
  .projects-preview-section.section-active::before,
  .contact-preview-section.section-active::before {
    opacity: 0.8;
  }

  .ai-help-preview-section.section-active::before {
    opacity: 0.9;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .cta-button:hover,
  .article-card:hover,
  .media-card:hover,
  .project-card:hover,
  .contact-option:hover {
    transform: none;
  }

  /* Disable section background transitions */
  .articles-preview-section,
  .shortform-preview-section,
  .ai-help-preview-section,
  .projects-preview-section,
  .contact-preview-section {
    transition: none;
  }

  .articles-preview-section::before,
  .shortform-preview-section::before,
  .ai-help-preview-section::before,
  .projects-preview-section::before,
  .contact-preview-section::before {
    transition: none;
    opacity: 0.3;
  }

  /* Keep AI Help section readable but static */
  .ai-help-preview-section.section-active {
    background: #4a5568;
    color: white;
  }

  .ai-help-preview-section.section-active .section-title,
  .ai-help-preview-section.section-active .section-subtitle {
    color: white;
    transition: none;
  }
}

/* Hero container positioning for constellation */
.hero-container {
  position: relative;
  min-height: 100vh;
}
</style>
