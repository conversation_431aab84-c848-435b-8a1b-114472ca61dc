# Matariki Journey Scroll Experience - Detailed Implementation Plan

## 🌟 Project Overview

Create a culturally meaningful scroll experience where the Matariki constellation revolves as users navigate through home page sections. Each star aligns with specific content areas, featuring subtle background patterns and animations that honor Māori cosmology while showcasing your work.

## 🎯 Core Concept & Three-State System

**"The Matariki Journey"** - As users scroll, they witness the constellation's story unfolding, with each star representing different aspects of your work and values.

### Section State Architecture
Each section follows a **3-state progression** controlled by ScrollTrigger:

#### State 1: Skeleton/Approaching (Pre-scroll)
- **Theme Elements**: Theme title and subtitle visible (e.g., "Pohutukawa - Reflection")
- **Appearance**: Minimal skeleton outlines, barely visible content placeholders
- **Background**: Transparent or very subtle theme color
- **GSAP Properties**: `opacity: 0`, `y: 50`, `scale: 0.8` for content elements

#### State 2: Entering/Active (ScrollTrigger start → midpoint)
- **Theme Transition**: Theme title/subtitle blend or move to background
- **Main Content**: Section title animates in prominently
- **Background**: Thematic patterns emerge (expanding from center, flowing, etc.)
- **GSAP Animation**: Progressive reveal with staggered timelines

#### State 3: Fully Loaded (Midpoint → end)
- **Content**: All elements visible and interactive
- **Background**: Full thematic animation (flowing lines, gentle movement)
- **Interactions**: Hover effects active, clickable elements enabled
- **Exit Behavior**: Dims to ~80% opacity when scrolling out (not back to skeleton)

### Special Considerations
- **Waitā (AI Help)**: Dark theme expansion - skeleton has dark frame that expands page-wide
- **Component-Based**: Each section as reusable component with unique behaviors
- **Theme Layering**: Theme elements stay underneath, main content reveals on top

**"Key Technologies"**
- HTML5: Semantic structure for sections and content
- CSS3: Styling, initial states, subtle gradients, transform properties for animation prep
- GSAP + ScrollTrigger: Core animation engine with scroll-linked animations
- Vue 3 Composition API: Component architecture and state management

## ⭐ Star-Section Mapping & State Specifications

| Star | Māori Name | Meaning | Section | Theme Title | State Behaviors |
|------|------------|---------|---------|-------------|-----------------|
| 1 | **Matariki** | Mother star, leadership | Hero | "Guiding Light - New Beginnings" | Radial expansion, dawn gradient |
| 2 | **Pohutukawa** | Reflection, remembrance | Articles | "Reflection - Connection & Growth" | Organic flowing curves, tree-like patterns |
| 3 | **Waitī** | Fresh water, nourishment | Shortform | "Fresh Flow - Quick Insights" | Droplet/bubble patterns, upward flow |
| 4 | **Waitā** | Salt water, vast knowledge | AI Help | "Deep Waters - Ocean of Wisdom" | **Dark frame expansion**, light-to-dark transition |
| 5 | **Waipuna-ā-rangi** | Rain, new growth | Projects | "New Growth - Building Tomorrow" | Spiral unfurling, rain ripple effects |
| 6 | **Ururangi** | Winds of change | Contact | "Winds of Change - Connection" | Flowing wind patterns, gentle movement |
| 7 | **Hiwa-i-te-rangi** | Dreams, aspirations | Footer/Future | "Dreams & Aspirations - Vision" | Upward reaching, star-like sparkles |

### Detailed Section State Specifications

#### 1. Hero Section (Matariki - Guiding Light)
- **State 1**: Theme title "Guiding Light - New Beginnings" visible, content skeleton
- **State 2**: Radial gradient expands from center, main hero content animates in
- **State 3**: Full dawn-light background, interactive CTAs, gentle pulsing effects
- **Unique Behavior**: Central star prominence, radiating light patterns

#### 2. Articles Section (Pohutukawa - Reflection)
- **State 1**: "Reflection - Connection & Growth" theme, article card skeletons
- **State 2**: Organic flowing curves emerge, article previews animate in staggered
- **State 3**: Full tree-branch-like background patterns, hover effects on cards
- **Unique Behavior**: Flowing organic lines that connect content elements

#### 3. Shortform Section (Waitī - Fresh Flow)
- **State 1**: "Fresh Flow - Quick Insights" theme, media placeholder boxes
- **State 2**: Droplet patterns appear, content bubbles up from bottom
- **State 3**: Subtle water-flow animation, interactive media thumbnails
- **Unique Behavior**: Upward flowing animation, fresh water bubble effects

#### 4. AI Help Section (Waitā - Deep Waters) **SPECIAL DARK THEME**
- **State 1**: "Deep Waters - Ocean of Wisdom" theme, **dark frame skeleton**
- **State 2**: **Dark frame expands page-wide**, deep wave gradients emerge
- **State 3**: Full dark theme with light text, ocean depth background animation
- **Unique Behavior**: **Complete theme inversion**, dark expansion effect
- **Technical Note**: This section triggers global theme change during scroll

#### 5. Projects Section (Waipuna-ā-rangi - New Growth)
- **State 1**: "New Growth - Building Tomorrow" theme, project card outlines
- **State 2**: Spiral patterns unfurl from center, project cards grow into view
- **State 3**: Rain ripple effects, fully interactive project showcases
- **Unique Behavior**: Spiral unfurling animation, growth-focused transitions

#### 6. Contact Section (Ururangi - Winds of Change)
- **State 1**: "Winds of Change - Connection" theme, contact option skeletons
- **State 2**: Flowing wind patterns sweep across, contact methods appear
- **State 3**: Gentle continuous movement, interactive contact elements
- **Unique Behavior**: Horizontal flowing patterns, wind-like movement

## 🎨 Visual Design Strategy
The Scrolling Marker: "Pae Arahī" (Guiding Path)
 It would contain 7 small, stylized star icons, each representing a section. As users scroll, the active star would be highlighted, guiding them through the content.

### Constellation Behavior
- **Rotation**: Stars rotate around a central point as user scrolls
- **Alignment**: Featured star moves to prominent position when section is active
- **Highlighting**: Active star glows, scales, and pulses
- **Connection**: Subtle lines connect stars, strengthening when section is active

### Background Patterns (Natural CSS Art)
Each section gets subtle natural patterns using abstract references:

1. **Hero (Matariki)**: Gentle radiating gradients (dawn light)
2. **Articles (Pohutukawa)**: Organic flowing curves (tree branches, water flow)
3. **Shortform (Waitī)**: Subtle droplet/bubble patterns (fresh water)
4. **AI Help (Waitā)**: Deep wave gradients with **light-to-dark transition** (ocean depths)
5. **Projects (Waipuna-ā-rangi)**: Growth spiral patterns (fern unfurling, rain ripples)
6. **Contact (Ururangi)**: Flowing wind patterns (gentle movement)

### Color Strategy (Refined)
- **Primary**: Existing kawakawa/fern green palette
- **Grays**: Full spectrum from light mist to charcoal
- **Accent**: Subtle red/coral for differentiation when needed
- **AI Help Section**: **Gradual light-to-dark transition** to prioritize this section
- **Transitions**: Smooth, barely perceptible color shifts
- **Philosophy**: Subtle and smart - effects should feel natural, not designed

## 🛠 Technical Architecture

### Dependencies Required
```json
{
  "gsap": "^3.12.2",
  "@gsap/scrolltrigger": "^3.12.2"
}
```

### Enhanced File Structure
```
src/
├── components/
│   ├── MatarikiConstellation.vue (existing - enhanced)
│   ├── sections/
│   │   ├── BaseScrollSection.vue (new - template component)
│   │   ├── HeroSection.vue (new - Matariki)
│   │   ├── ArticlesSection.vue (new - Pohutukawa)
│   │   ├── ShortformSection.vue (new - Waitī)
│   │   ├── AIHelpSection.vue (new - Waitā - special dark theme)
│   │   ├── ProjectsSection.vue (new - Waipuna-ā-rangi)
│   │   └── ContactSection.vue (new - Ururangi)
├── composables/
│   ├── useMatarikiAnimation.ts (existing - enhanced)
│   ├── useScrollSections.ts (new)
│   ├── useSectionStates.ts (new)
│   └── useThemeTransitions.ts (new - for Waitā dark theme)
├── assets/
│   └── styles/
│       ├── matariki-patterns.css (enhanced)
│       ├── section-states.css (new)
│       ├── theme-transitions.css (new)
│       └── constellation-animations.css (existing)
└── views/
    └── HomeView.vue (refactored to use section components)
```

### Component Architecture

#### BaseScrollSection.vue (Template Component)
```typescript
interface SectionProps {
  sectionId: string
  starName: string
  themeTitle: string
  themeSubtitle: string
  mainTitle: string
  darkTheme?: boolean
  animationConfig?: SectionAnimationConfig
}

interface SectionAnimationConfig {
  skeletonAnimation: GSAPAnimation
  enteringAnimation: GSAPAnimation
  fullyLoadedAnimation: GSAPAnimation
  exitAnimation: GSAPAnimation
}
```

#### Section State Management
```typescript
// useSectionStates.ts
export const useSectionStates = (sectionId: string) => {
  const currentState = ref<'skeleton' | 'entering' | 'loaded' | 'exiting'>('skeleton')
  const progress = ref(0)

  const setupScrollTrigger = () => {
    ScrollTrigger.create({
      trigger: `[data-section="${sectionId}"]`,
      start: "top 80%",
      end: "bottom 20%",
      onUpdate: (self) => {
        progress.value = self.progress
        updateSectionState(self.progress)
      }
    })
  }

  return { currentState, progress, setupScrollTrigger }
}
```

### Animation Timeline
1. **Page Load**: Stars fade in with staggered timing
2. **Scroll Start**: Constellation begins rotation
3. **Section Enter**: Featured star highlights, background pattern emerges
4. **Section Active**: Star pulses, pattern fully visible
5. **Section Exit**: Star dims, pattern fades
6. **Scroll End**: Constellation completes cycle

## 📋 Implementation Status

### ✅ Phase 1: Foundation - COMPLETE
- ✅ Research Matariki cultural significance
- ✅ Install and configure GSAP
- ✅ Create basic constellation positioning system
- ✅ Build component architecture with BaseScrollSection
- ✅ Implement useSectionStates composable

### 🚧 Phase 2: Section Components - IN PROGRESS
- ✅ ArticlesSection (Pohutukawa) - Complete with organic flow patterns
- ✅ AIHelpSection (Waitā) - Complete with dark theme expansion
- ⏳ HeroSection (Matariki) - Needs conversion to component
- ⏳ ShortformSection (Waitī) - Needs creation
- ⏳ ProjectsSection (Waipuna-ā-rangi) - Needs creation
- ⏳ ContactSection (Ururangi) - Needs creation

### ⏳ Phase 3: Polish & Integration - PENDING
- ⏳ Constellation star highlighting sync with sections
- ⏳ Responsive design optimization
- ⏳ Accessibility features implementation
- ⏳ Performance optimization and testing

## 🎯 Current Implementation Details

### What's Working Now:
1. **BaseScrollSection Component**: Template for all sections with 3-state system
2. **ArticlesSection**: Full implementation with skeleton → entering → loaded states
3. **AIHelpSection**: Special dark theme with expansion animation
4. **Section State Management**: Smooth transitions between skeleton, entering, loaded, exiting
5. **Background Patterns**: SVG-based organic patterns that animate on scroll
6. **Theme Layering**: Theme titles that blend into background as main content appears

### Key Features Implemented:
- **Skeleton Loading**: Shimmer animations for initial state
- **Progressive Enhancement**: Content reveals in stages
- **Dark Theme Expansion**: Waitā section transforms entire page theme
- **Organic Animations**: Natural flowing patterns using SVG paths
- **Responsive Design**: Mobile-first approach with breakpoints

## 🎪 User Experience Flow

1. **Landing**: User sees Matariki constellation in hero section
2. **Discovery**: As they scroll, stars begin to move and rotate
3. **Engagement**: Each section reveals its star with unique background
4. **Connection**: User understands the relationship between stars and content
5. **Completion**: Full constellation journey tells your story

## 📱 Responsive Considerations

- **Desktop**: Full constellation visible, complex patterns
- **Tablet**: Simplified patterns, maintained star movement
- **Mobile**: Essential stars only, minimal patterns for performance
- **Reduced Motion**: Static constellation with subtle highlights

## ♿ Accessibility Features

- **Reduced Motion**: Respect `prefers-reduced-motion` setting
- **Focus Management**: Keyboard navigation support
- **Screen Readers**: Meaningful alt text and ARIA labels
- **Color Contrast**: Maintain WCAG AA compliance
- **Performance**: Smooth 60fps animations

## 🚀 Success Metrics

- **Engagement**: Increased time on home page
- **Navigation**: Higher click-through to section pages
- **Performance**: Maintain <100ms interaction response
- **Accessibility**: Pass WAVE and axe audits
- **Cultural Authenticity**: Respectful representation of Māori values

---

## 📝 What You Need to Provide

## ✅ **Design Decisions Made**

### Cultural Approach
- **Natural Abstractions**: Ferns, pohutukawa, water patterns - no obvious tribal designs
- **Star Meanings**: Confirmed and loved - each star represents different work aspects
- **Respectful Representation**: Abstract natural elements honor cultural significance

### Visual Strategy
- **Subtle & Smart**: Effects should feel natural, not over-designed
- **Color Palette**: Current kawakawa/fern + gray spectrum + subtle red accent
- **AI Help Priority**: Gradual light-to-dark transition to emphasize this section
- **Scroll-Only**: Stars are visual elements, not clickable navigation

### Technical Approach
- **Progressive Enhancement**: Core experience first, enhancements later
- **Performance Focus**: Smooth, optimized animations
- **Future Considerations**: Seasonal changes, loading strategies can be added later

## 🚀 How to Use the New Section Components

### Creating a New Section Component

1. **Extend BaseScrollSection**:
```vue
<script setup lang="ts">
import BaseScrollSection from './BaseScrollSection.vue'

// Your section-specific logic here
</script>

<template>
  <BaseScrollSection
    section-id="your-section"
    star-name="star-name"
    theme-title="Theme Title"
    theme-subtitle="Theme • Subtitle • Elements"
    main-title="Main Section Title"
    background-pattern="pattern-name"
    :dark-theme="false"
    @section-active="handleSectionActive"
  >
    <!-- Skeleton content -->
    <template #skeleton>
      <div class="your-skeleton-content">
        <!-- Skeleton elements here -->
      </div>
    </template>

    <!-- Header content -->
    <template #header>
      <p class="section-subtitle">Your subtitle</p>
    </template>

    <!-- Main content -->
    <template #content>
      <div class="your-main-content">
        <!-- Your content here -->
      </div>
    </template>
  </BaseScrollSection>
</template>
```

2. **Add to HomeView.vue**:
```vue
import YourSection from '../components/sections/YourSection.vue'

<YourSection @section-active="handleSectionActive" />
```

### Section State System

Each section automatically progresses through these states:
- **skeleton** (0-10% scroll): Theme visible, skeleton elements showing
- **entering** (10-50% scroll): Theme fades, background patterns emerge, content starts appearing
- **loaded** (50-90% scroll): Full content visible, interactive elements active
- **exiting** (90-100% scroll): Content dims slightly as user scrolls away

### Background Patterns

Create custom SVG patterns in your section template:
```vue
<template #background-pattern>
  <div class="pattern-your-pattern">
    <svg viewBox="0 0 1200 800">
      <!-- Your SVG pattern here -->
    </svg>
  </div>
</template>
```

## 📝 Next Steps

### Immediate Tasks:
1. **Convert Hero Section** to use BaseScrollSection component
2. **Create ShortformSection** (Waitī) with fresh water bubble patterns
3. **Create ProjectsSection** (Waipuna-ā-rangi) with spiral growth patterns
4. **Create ContactSection** (Ururangi) with wind flow patterns

### Integration Tasks:
1. **Sync Constellation Stars** with section states
2. **Add Matariki Star Highlighting** when sections become active
3. **Implement Global Theme Transitions** for smooth dark/light changes
4. **Add Reduced Motion Support** for accessibility

### Polish Tasks:
1. **Performance Optimization** - lazy load heavy animations
2. **Mobile Responsiveness** - test and refine on all devices
3. **Accessibility Audit** - ensure WCAG compliance
4. **User Testing** - gather feedback on scroll experience

Ready to continue implementation! 🌟

---

## 🔧 Development Notes

### Current File Structure:
```
src/
├── components/
│   ├── sections/
│   │   ├── BaseScrollSection.vue ✅
│   │   ├── ArticlesSection.vue ✅
│   │   └── AIHelpSection.vue ✅
│   └── MatarikiConstellation.vue (existing)
├── composables/
│   ├── useSectionStates.ts ✅
│   └── useMatarikiAnimation.ts (existing)
└── views/
    └── HomeView.vue (updated to use new components)
```

### Testing:
- Development server running at http://localhost:5173/
- Components are rendering without errors
- Scroll animations are functional
- Dark theme expansion working for AI Help section
