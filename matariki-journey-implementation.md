# Matariki Journey Scroll Experience - Implementation Plan

## 🌟 Project Overview

Create a culturally meaningful scroll experience where the Matariki constellation revolves as users navigate through home page sections. Each star aligns with specific content areas, featuring subtle background patterns and animations that honor Māori cosmology while showcasing your work.

## 🎯 Core Concept

**"The Matariki Journey"** - As users scroll, they witness the constellation's story unfolding, with each star representing different aspects of your work and values.

HTML5: Semantic structure for sections and content.
CSS3: Styling, initial states, subtle gradients, transform properties for animation pre-prep.
JavaScript:
GSAP (GreenSock Animation Platform): The core animation engine.
ScrollTrigger Plugin: Essential for linking animations to scroll position.
Intersection Observer API (Optional but Recommended): For precise loading/unloading of heavy elements if you have many.

## ⭐ Star-Section Mapping

| Star | Māori Name | Meaning | Section | Design Theme |
|------|------------|---------|---------|--------------|
| 1 | **Matariki** | Mother star, leadership | Hero | Central leadership, brand identity |
| 2 | **<PERSON>hutu<PERSON>** | Reflection, remembrance | Articles | Deep thinking, contemplation |
| 3 | **Waitī** | Fresh water, nourishment | Shortform | Quick insights, refreshing content |
| 4 | **Waitā** | Salt water, vast knowledge | AI Help | Ocean of wisdom, depth |
| 5 | **Waipuna-ā-rangi** | Rain, new growth | Projects | Growth, development, potential |
| 6 | **Ururangi** | Winds of change | Contact | Connection, collaboration, movement |
| 7 | **Hiwa-i-te-rangi** | Dreams, aspirations | Footer/Future | Hope, vision, tomorrow |

## 🎨 Visual Design Strategy
The Scrolling Marker: "Pae Arahī" (Guiding Path)
 It would contain 7 small, stylized star icons, each representing a section. As users scroll, the active star would be highlighted, guiding them through the content.

### Constellation Behavior
- **Rotation**: Stars rotate around a central point as user scrolls
- **Alignment**: Featured star moves to prominent position when section is active
- **Highlighting**: Active star glows, scales, and pulses
- **Connection**: Subtle lines connect stars, strengthening when section is active

### Background Patterns (Natural CSS Art)
Each section gets subtle natural patterns using abstract references:

1. **Hero (Matariki)**: Gentle radiating gradients (dawn light)
2. **Articles (Pohutukawa)**: Organic flowing curves (tree branches, water flow)
3. **Shortform (Waitī)**: Subtle droplet/bubble patterns (fresh water)
4. **AI Help (Waitā)**: Deep wave gradients with **light-to-dark transition** (ocean depths)
5. **Projects (Waipuna-ā-rangi)**: Growth spiral patterns (fern unfurling, rain ripples)
6. **Contact (Ururangi)**: Flowing wind patterns (gentle movement)

### Color Strategy (Refined)
- **Primary**: Existing kawakawa/fern green palette
- **Grays**: Full spectrum from light mist to charcoal
- **Accent**: Subtle red/coral for differentiation when needed
- **AI Help Section**: **Gradual light-to-dark transition** to prioritize this section
- **Transitions**: Smooth, barely perceptible color shifts
- **Philosophy**: Subtle and smart - effects should feel natural, not designed

## 🛠 Technical Architecture

### Dependencies Required
```json
{
  "gsap": "^3.12.2",
  "@gsap/scrolltrigger": "^3.12.2"
}
```

### File Structure
```
src/
├── components/
│   ├── MatarikiConstellation.vue
│   └── SectionBackground.vue
├── composables/
│   ├── useMatarikiAnimation.ts
│   └── useScrollSections.ts
├── assets/
│   └── styles/
│       ├── matariki-patterns.css
│       └── constellation-animations.css
└── views/
    └── HomeView.vue (enhanced)
```

### Animation Timeline
1. **Page Load**: Stars fade in with staggered timing
2. **Scroll Start**: Constellation begins rotation
3. **Section Enter**: Featured star highlights, background pattern emerges
4. **Section Active**: Star pulses, pattern fully visible
5. **Section Exit**: Star dims, pattern fades
6. **Scroll End**: Constellation completes cycle

## 📋 Implementation Phases

### Phase 1: Foundation (Tasks 1-3)
- Research Matariki cultural significance
- Install and configure GSAP
- Create basic constellation positioning system

### Phase 2: Visual Design (Tasks 4-6)
- Design CSS art patterns for each section
- Implement star animation and highlighting
- Create smooth background transitions

### Phase 3: Polish & Optimization (Tasks 7-9)
- Ensure responsive design across devices
- Implement accessibility features
- Test and refine user experience

## 🎪 User Experience Flow

1. **Landing**: User sees Matariki constellation in hero section
2. **Discovery**: As they scroll, stars begin to move and rotate
3. **Engagement**: Each section reveals its star with unique background
4. **Connection**: User understands the relationship between stars and content
5. **Completion**: Full constellation journey tells your story

## 📱 Responsive Considerations

- **Desktop**: Full constellation visible, complex patterns
- **Tablet**: Simplified patterns, maintained star movement
- **Mobile**: Essential stars only, minimal patterns for performance
- **Reduced Motion**: Static constellation with subtle highlights

## ♿ Accessibility Features

- **Reduced Motion**: Respect `prefers-reduced-motion` setting
- **Focus Management**: Keyboard navigation support
- **Screen Readers**: Meaningful alt text and ARIA labels
- **Color Contrast**: Maintain WCAG AA compliance
- **Performance**: Smooth 60fps animations

## 🚀 Success Metrics

- **Engagement**: Increased time on home page
- **Navigation**: Higher click-through to section pages
- **Performance**: Maintain <100ms interaction response
- **Accessibility**: Pass WAVE and axe audits
- **Cultural Authenticity**: Respectful representation of Māori values

---

## 📝 What You Need to Provide

## ✅ **Design Decisions Made**

### Cultural Approach
- **Natural Abstractions**: Ferns, pohutukawa, water patterns - no obvious tribal designs
- **Star Meanings**: Confirmed and loved - each star represents different work aspects
- **Respectful Representation**: Abstract natural elements honor cultural significance

### Visual Strategy
- **Subtle & Smart**: Effects should feel natural, not over-designed
- **Color Palette**: Current kawakawa/fern + gray spectrum + subtle red accent
- **AI Help Priority**: Gradual light-to-dark transition to emphasize this section
- **Scroll-Only**: Stars are visual elements, not clickable navigation

### Technical Approach
- **Progressive Enhancement**: Core experience first, enhancements later
- **Performance Focus**: Smooth, optimized animations
- **Future Considerations**: Seasonal changes, loading strategies can be added later

Ready to begin implementation! 🌟
