<template>
  <div class="project-page">
    <div class="site-container">
      <header class="project-header">
        <nav class="breadcrumb">
          <router-link to="/projects" class="breadcrumb-link">Projects</router-link>
          <span class="breadcrumb-separator">/</span>
          <span class="breadcrumb-current">{{ projectTitle }}</span>
        </nav>
        <h1>{{ projectTitle }}</h1>
        <p class="project-subtitle">{{ projectDescription }}</p>
      </header>

      <section class="project-content">
        <div class="project-details">
          <h2>Project Details</h2>
          <p>Detailed information about project: <strong>{{ $route.params.id }}</strong></p>
          <p>This page will contain comprehensive information about the selected project, including:</p>
          <ul>
            <li>Project overview and objectives</li>
            <li>Technologies and tools used</li>
            <li>Development timeline</li>
            <li>Key features and outcomes</li>
            <li>Lessons learned</li>
            <li>Links to live demos or repositories</li>
          </ul>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// Simple project data - in a real app this would come from an API or store
const projectData: Record<string, { title: string; description: string }> = {
  'frame-check': {
    title: 'Frame Check Newsletter',
    description: 'Honest ROI analysis of AI tools for busy professionals'
  },
  'cstpit-framework': {
    title: 'C-S-T & P-I-T Framework',
    description: 'Structured approach to AI prompting for consistent results'
  },
  'ai-workshops': {
    title: 'AI Tool Workshops',
    description: 'Interactive workshops for practical AI tool usage'
  }
}

const projectId = computed(() => route.params.id as string)
const currentProject = computed(() => projectData[projectId.value] || { title: 'Unknown Project', description: 'Project not found' })
const projectTitle = computed(() => currentProject.value.title)
const projectDescription = computed(() => currentProject.value.description)
</script>

<style scoped>
.project-page {
  padding: var(--space-8) 0;
}

.project-header {
  margin-bottom: var(--space-8);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
  font-size: 0.875rem;
}

.breadcrumb-link {
  color: var(--color-kawakawa);
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-link:hover {
  color: var(--color-fern);
  text-decoration: underline;
}

.breadcrumb-separator {
  color: var(--color-text-muted);
}

.breadcrumb-current {
  color: var(--color-text-muted);
}

.project-header h1 {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-3);
}

.project-subtitle {
  font-size: 1.125rem;
  color: var(--color-text-muted);
  line-height: 1.6;
}

.project-content {
  background: var(--color-base);
  border: 1px solid var(--color-mist);
  border-radius: 8px;
  padding: var(--space-6);
}

.project-details h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-4);
  border-bottom: 2px solid var(--color-kawakawa);
  padding-bottom: var(--space-2);
}

.project-details p {
  color: var(--color-text-muted);
  line-height: 1.6;
  margin-bottom: var(--space-4);
}

.project-details ul {
  color: var(--color-text-muted);
  line-height: 1.6;
  padding-left: var(--space-5);
}

.project-details li {
  margin-bottom: var(--space-2);
}
</style>
