<template>
  <div class="projects-page">
    <div class="site-container">
      <header class="page-header">
        <h1>Projects</h1>
        <p class="page-subtitle">
          Explore my current and past projects, from AI tools and frameworks to content creation and community initiatives.
        </p>
      </header>

      <section class="projects-section">
        <h2>Current Projects</h2>
        <div class="projects-grid">
          <article class="project-card">
            <div class="project-content">
              <h3>Frame Check Newsletter</h3>
              <p class="project-description">
                A Substack newsletter providing honest ROI analysis of AI tools for busy professionals,
                incorporating New Zealand values and practical wisdom.
              </p>
              <div class="project-meta">
                <span class="project-type">Newsletter</span>
                <span class="project-status">Active</span>
              </div>
              <div class="project-links">
                <a href="#" target="_blank" class="project-link">
                  📰 Read on Substack
                </a>
              </div>
            </div>
          </article>

          <article class="project-card">
            <div class="project-content">
              <h3>C-S-T & P-I-T Framework</h3>
              <p class="project-description">
                A structured approach to AI prompting that ensures consistent, high-quality results
                through clear context, skills, tasks, purpose, ingredients, and targets.
              </p>
              <div class="project-meta">
                <span class="project-type">Framework</span>
                <span class="project-status">Complete</span>
              </div>
              <div class="project-links">
                <router-link to="/ai-help/cstpit-framework" class="project-link">
                  📋 View Guide
                </router-link>
              </div>
            </div>
          </article>
        </div>
      </section>

      <section class="future-projects-section">
        <h2>Upcoming Projects</h2>
        <div class="projects-grid">
          <article class="project-card future">
            <div class="project-content">
              <h3>AI Tool Workshops</h3>
              <p class="project-description">
                Interactive workshops teaching practical AI tool usage with hands-on exercises
                and real-world applications. Coming soon to the AI Help section.
              </p>
              <div class="project-meta">
                <span class="project-type">Education</span>
                <span class="project-status">Planning</span>
              </div>
            </div>
          </article>
        </div>
      </section>
    </div>
  </div>
</template>

<style scoped>
.projects-page {
  padding: var(--space-8) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--space-12);
}

.page-header h1 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.page-subtitle {
  font-size: 1.125rem;
  color: var(--color-text-muted);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.projects-section,
.future-projects-section {
  margin-bottom: var(--space-12);
}

.projects-section h2,
.future-projects-section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-6);
  border-bottom: 2px solid var(--color-kawakawa);
  padding-bottom: var(--space-2);
}

.projects-grid {
  display: grid;
  gap: var(--space-6);
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .projects-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.project-card {
  background: var(--color-base);
  border: 1px solid var(--color-mist);
  border-radius: 8px;
  padding: var(--space-6);
  transition: all 0.2s ease;
}

.project-card:hover {
  border-color: var(--color-kawakawa);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.project-card.future {
  opacity: 0.8;
  border-style: dashed;
}

.project-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-3);
}

.project-description {
  color: var(--color-text-muted);
  line-height: 1.6;
  margin-bottom: var(--space-4);
}

.project-meta {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.project-type,
.project-status {
  font-size: 0.875rem;
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
  font-weight: 500;
}

.project-type {
  background: var(--color-mist);
  color: var(--color-text);
}

.project-status {
  background: var(--color-kawakawa);
  color: white;
}

.project-card.future .project-status {
  background: var(--color-text-muted);
}

.project-links {
  display: flex;
  gap: var(--space-3);
}

.project-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  color: var(--color-kawakawa);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  transition: color 0.2s ease;
}

.project-link:hover {
  color: var(--color-fern);
  text-decoration: underline;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .project-card:hover {
    transform: none;
  }
}
</style>
