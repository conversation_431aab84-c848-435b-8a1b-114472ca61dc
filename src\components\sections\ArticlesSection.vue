<script setup lang="ts">
import { onMounted } from 'vue'
import BaseScrollSection from './BaseScrollSection.vue'
import { gsap } from 'gsap'

// Articles-specific animation configuration
const articlesAnimationConfig = {
  enteringToLoaded: {
    duration: 1.2,
    ease: "power2.out"
  },
  customAnimations: {
    organicFlow: {
      duration: 2,
      ease: "sine.inOut",
      repeat: -1,
      yoyo: true
    }
  }
}

// Sample articles data (would come from props or store in real implementation)
const articles = [
  {
    id: 1,
    title: "Claude vs ChatGPT: The Real ROI Analysis",
    excerpt: "After 6 months of testing both tools in real business scenarios, here's what actually matters for your bottom line.",
    date: "Dec 15, 2024",
    category: "AI Tools",
    readTime: "8 min read"
  },
  {
    id: 2,
    title: "The Tikanga of AI: Respectful Innovation",
    excerpt: "How traditional Māori values can guide ethical AI implementation in modern workplaces.",
    date: "Dec 10, 2024",
    category: "Ethics",
    readTime: "12 min read"
  },
  {
    id: 3,
    title: "Prompt Engineering: Beyond the Hype",
    excerpt: "Real frameworks that work, tested with 500+ business prompts. No fluff, just results.",
    date: "Dec 5, 2024",
    category: "Frameworks",
    readTime: "15 min read"
  }
]

const handleSectionActive = () => {
  // Trigger organic flowing animation when section becomes active
  const backgroundPattern = document.querySelector('.pattern-organic-flow')
  if (backgroundPattern) {
    gsap.to(backgroundPattern.querySelectorAll('.flow-line'), {
      strokeDashoffset: 0,
      duration: 3,
      stagger: 0.2,
      ease: "power2.inOut"
    })
  }
}

onMounted(() => {
  // Initialize organic flow pattern
  const flowLines = document.querySelectorAll('.flow-line')
  flowLines.forEach(line => {
    const length = (line as SVGPathElement).getTotalLength()
    gsap.set(line, {
      strokeDasharray: length,
      strokeDashoffset: length
    })
  })
})
</script>

<template>
  <BaseScrollSection
    section-id="articles"
    star-name="pohutukawa"
    theme-title="Pohutukawa"
    theme-subtitle="Reflection • Connection • Growth"
    main-title="Latest from Frame Check"
    background-pattern="organic-flow"
    class-name="articles-section"
    @section-active="handleSectionActive"
  >
    <!-- Skeleton Content -->
    <template #skeleton>
      <div class="articles-skeleton">
        <div class="skeleton-card" v-for="n in 3" :key="n">
          <div class="skeleton-image"></div>
          <div class="skeleton-content">
            <div class="skeleton-meta"></div>
            <div class="skeleton-title"></div>
            <div class="skeleton-excerpt"></div>
          </div>
        </div>
      </div>
    </template>

    <!-- Header Content -->
    <template #header>
      <p class="section-subtitle" data-gsap="section-subtitle">
        Honest AI tool reviews and practical insights
      </p>
      <RouterLink to="/articles" class="section-cta" data-gsap="section-cta">
        View All Articles →
      </RouterLink>
    </template>

    <!-- Main Content -->
    <template #content>
      <div class="articles-grid" data-gsap="articles-grid">
        <article 
          v-for="article in articles" 
          :key="article.id"
          class="article-card"
          data-gsap="article-card"
        >
          <div class="article-image">
            <div class="article-category-badge">{{ article.category }}</div>
          </div>
          <div class="article-content">
            <div class="article-meta">
              <span class="article-date">{{ article.date }}</span>
              <span class="article-read-time">{{ article.readTime }}</span>
            </div>
            <h3 class="article-title">{{ article.title }}</h3>
            <p class="article-excerpt">{{ article.excerpt }}</p>
            <RouterLink :to="`/articles/${article.id}`" class="article-link">
              Read More →
            </RouterLink>
          </div>
        </article>
      </div>
    </template>

    <!-- Background Pattern - Organic Flow -->
    <template #background-pattern>
      <div class="pattern-organic-flow">
        <svg class="organic-flow-svg" viewBox="0 0 1200 800" preserveAspectRatio="xMidYMid slice">
          <defs>
            <linearGradient id="flowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:var(--color-kawakawa);stop-opacity:0.1" />
              <stop offset="50%" style="stop-color:var(--color-fern);stop-opacity:0.2" />
              <stop offset="100%" style="stop-color:var(--color-kawakawa);stop-opacity:0.1" />
            </linearGradient>
          </defs>
          
          <!-- Organic flowing lines representing tree branches/water flow -->
          <path 
            class="flow-line"
            d="M0,400 Q200,300 400,350 T800,300 Q1000,250 1200,300"
            stroke="url(#flowGradient)"
            stroke-width="2"
            fill="none"
            opacity="0.6"
          />
          <path 
            class="flow-line"
            d="M0,500 Q300,400 600,450 T1200,400"
            stroke="url(#flowGradient)"
            stroke-width="1.5"
            fill="none"
            opacity="0.4"
          />
          <path 
            class="flow-line"
            d="M200,200 Q400,150 600,200 T1000,180 Q1100,160 1200,180"
            stroke="url(#flowGradient)"
            stroke-width="1"
            fill="none"
            opacity="0.3"
          />
          
          <!-- Subtle connection nodes -->
          <circle cx="400" cy="350" r="3" fill="var(--color-kawakawa)" opacity="0.2" />
          <circle cx="800" cy="300" r="2" fill="var(--color-fern)" opacity="0.3" />
          <circle cx="600" cy="450" r="2.5" fill="var(--color-kawakawa)" opacity="0.2" />
        </svg>
      </div>
    </template>
  </BaseScrollSection>
</template>

<style scoped>
.articles-section {
  background: linear-gradient(135deg, var(--color-base) 0%, rgba(245, 247, 250, 0.8) 100%);
}

/* Skeleton Styles */
.articles-skeleton {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  max-width: 1200px;
  margin: 0 auto;
}

.skeleton-card {
  display: flex;
  gap: var(--space-4);
  padding: var(--space-4);
  border: 1px solid var(--color-mist);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.5);
}

.skeleton-image {
  width: 200px;
  height: 120px;
  background: linear-gradient(90deg, var(--color-mist) 25%, rgba(255,255,255,0.5) 50%, var(--color-mist) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 8px;
  flex-shrink: 0;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.skeleton-meta {
  height: 1rem;
  width: 30%;
  background: linear-gradient(90deg, var(--color-mist) 25%, rgba(255,255,255,0.5) 50%, var(--color-mist) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite 0.3s;
  border-radius: 4px;
}

.skeleton-title {
  height: 1.5rem;
  width: 80%;
  background: linear-gradient(90deg, var(--color-mist) 25%, rgba(255,255,255,0.5) 50%, var(--color-mist) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite 0.6s;
  border-radius: 6px;
}

.skeleton-excerpt {
  height: 3rem;
  width: 90%;
  background: linear-gradient(90deg, var(--color-mist) 25%, rgba(255,255,255,0.5) 50%, var(--color-mist) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite 0.9s;
  border-radius: 6px;
}

/* Main Content Styles */
.section-subtitle {
  font-size: var(--text-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
}

.section-cta {
  display: inline-flex;
  align-items: center;
  color: var(--color-kawakawa);
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.section-cta:hover {
  color: var(--color-fern);
  transform: translateX(4px);
}

.articles-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  max-width: 1200px;
  margin: 0 auto;
}

.article-card {
  display: flex;
  gap: var(--space-4);
  background: white;
  border: 1px solid var(--color-mist);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.article-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--color-kawakawa);
}

.article-image {
  width: 200px;
  height: 120px;
  background: linear-gradient(135deg, var(--color-kawakawa) 0%, var(--color-fern) 100%);
  position: relative;
  flex-shrink: 0;
}

.article-category-badge {
  position: absolute;
  top: var(--space-2);
  left: var(--space-2);
  background: rgba(255, 255, 255, 0.9);
  color: var(--color-kawakawa);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: var(--text-xs);
  font-weight: 500;
}

.article-content {
  flex: 1;
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
}

.article-meta {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
  color: var(--color-text-secondary);
}

.article-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-2);
  line-height: 1.3;
}

.article-excerpt {
  color: var(--color-text-secondary);
  line-height: 1.5;
  margin-bottom: var(--space-4);
  flex: 1;
}

.article-link {
  color: var(--color-kawakawa);
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.article-link:hover {
  color: var(--color-fern);
  transform: translateX(4px);
}

/* Background Pattern */
.pattern-organic-flow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.organic-flow-svg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .skeleton-card,
  .article-card {
    flex-direction: column;
  }
  
  .skeleton-image,
  .article-image {
    width: 100%;
    height: 160px;
  }
  
  .articles-grid {
    gap: var(--space-4);
  }
}

/* Shimmer animation */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>
