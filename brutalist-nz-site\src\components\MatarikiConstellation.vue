<script setup lang="ts">
import { onMounted } from 'vue'
import { useMatarikiAnimation } from '../composables/useMatarikiAnimation'

const { stars, currentActiveSection, initializeConstellation } = useMatarikiAnimation()

onMounted(() => {
  initializeConstellation()
})
</script>

<template>
  <div class="matariki-constellation" data-gsap="constellation">
    <!-- Central point for rotation -->
    <div class="constellation-center"></div>
    
    <!-- Individual stars -->
    <div
      v-for="star in stars"
      :key="star.id"
      :data-star="star.id"
      :data-section="star.section"
      class="star"
      :class="`star-${star.id}`"
      :title="`${star.name} - ${star.meaning}`"
    >
      <div class="star-core"></div>
      <div class="star-glow"></div>
    </div>

    <!-- Constellation connections (subtle lines) -->
    <svg class="constellation-lines" viewBox="0 0 300 300">
      <defs>
        <linearGradient id="starConnection" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:var(--color-kawakawa);stop-opacity:0.1" />
          <stop offset="50%" style="stop-color:var(--color-kawakawa);stop-opacity:0.3" />
          <stop offset="100%" style="stop-color:var(--color-kawakawa);stop-opacity:0.1" />
        </linearGradient>
      </defs>
      
      <!-- Connection lines between stars (will be animated) -->
      <g class="star-connections">
        <path 
          class="connection-line"
          d="M150,30 L200,80 L180,150 L120,180 L70,120 L90,60 Z"
          stroke="url(#starConnection)"
          stroke-width="1"
          fill="none"
          opacity="0.2"
        />
      </g>
    </svg>
  </div>
</template>

<style scoped>
.matariki-constellation {
  position: fixed;
  top: 0;
  right: 0;
  width: 300px;
  height: 100vh;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  overflow: hidden;
}

.constellation-center {
  position: absolute;
  width: 2px;
  height: 2px;
  background: var(--color-kawakawa);
  border-radius: 50%;
  opacity: 0.3;
  z-index: 1;
}

.star {
  position: absolute;
  width: 16px;
  height: 16px;
  cursor: default;
  z-index: 2;
  /* Center the star initially */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s ease;
}

.star.active {
  width: 24px;
  height: 24px;
}

.star-core {
  width: 100%;
  height: 100%;
  background: var(--color-kawakawa);
  border-radius: 50%;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.star-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, var(--color-kawakawa) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0.3;
  z-index: 1;
  transition: all 0.4s ease;
}

.star.active .star-glow {
  opacity: 0.6;
  width: 300%;
  height: 300%;
  top: -75%;
  left: -75%;
}

/* Individual star styling */
.star-matariki .star-core {
  background: var(--color-kawakawa);
  width: 14px;
  height: 14px;
}

.star-pohutukawa .star-core {
  background: var(--color-fern);
}

.star-waiti .star-core {
  background: #4A90E2; /* Fresh water blue */
}

.star-waita .star-core {
  background: #2C5282; /* Deep ocean blue */
}

.star-waipuna-a-rangi .star-core {
  background: var(--color-kawakawa);
}

.star-ururangi .star-core {
  background: #718096; /* Wind gray */
}

.star-hiwa-i-te-rangi .star-core {
  background: #E53E3E; /* Dreams red accent */
}

/* Constellation lines */
.constellation-lines {
  position: absolute;
  width: 300px;
  height: 300px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 0;
}

.connection-line {
  transition: opacity 0.6s ease;
}

/* Animation states */
.star:hover .star-core {
  transform: scale(1.2);
}

.star:hover .star-glow {
  opacity: 0.6;
  transform: scale(1.1);
}

/* Active section highlighting */
.section-active .star-connections .connection-line {
  opacity: 0.4;
  stroke-width: 1.5;
}

/* Responsive design */
@media (max-width: 1024px) {
  .matariki-constellation {
    width: 40%;
  }
  
  .star {
    width: 10px;
    height: 10px;
  }
  
  .star-matariki .star-core {
    width: 12px;
    height: 12px;
  }
}

@media (max-width: 768px) {
  .matariki-constellation {
    width: 35%;
    opacity: 0.7;
  }
  
  .star {
    width: 8px;
    height: 8px;
  }
  
  .star-matariki .star-core {
    width: 10px;
    height: 10px;
  }
  
  .constellation-lines {
    width: 200px;
    height: 200px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .star-core,
  .star-glow,
  .connection-line {
    transition: none;
  }
  
  .star:hover .star-core {
    transform: none;
  }
  
  .star:hover .star-glow {
    transform: none;
  }
}

/* Accessibility */
.star:focus {
  outline: 2px solid var(--color-kawakawa);
  outline-offset: 2px;
  border-radius: 50%;
}

/* Dark mode considerations for AI Help section */
@media (prefers-color-scheme: dark) {
  .star-waita .star-glow {
    opacity: 0.5;
  }
}
</style>
