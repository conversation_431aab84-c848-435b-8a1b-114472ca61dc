import { ref, onMounted, onUnmounted } from 'vue'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger)

export interface MatarikiStar {
  id: string
  name: string
  meaning: string
  section: string
  angle: number
  radius: number
  element?: HTMLElement
}

export const useMatarikiAnimation = () => {
  const isInitialized = ref(false)
  const currentActiveSection = ref('')
  const animationTimeline = ref<gsap.core.Timeline | null>(null)

  // Matariki constellation configuration
  const stars = ref<MatarikiStar[]>([
    {
      id: 'matariki',
      name: '<PERSON><PERSON><PERSON>',
      meaning: 'Mother star, leadership',
      section: 'hero',
      angle: 0,
      radius: 120
    },
    {
      id: 'pohutukawa',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      meaning: 'Reflection, remembrance',
      section: 'articles',
      angle: 51.4, // 360/7 degrees apart
      radius: 100
    },
    {
      id: 'waiti',
      name: 'Wait<PERSON>',
      meaning: 'Fresh water, nourishment',
      section: 'shortform',
      angle: 102.8,
      radius: 110
    },
    {
      id: 'waita',
      name: 'Wait<PERSON>',
      meaning: 'Salt water, vast knowledge',
      section: 'ai-help',
      angle: 154.2,
      radius: 95
    },
    {
      id: 'waipuna-a-rangi',
      name: 'Waipuna-ā-rangi',
      meaning: 'Rain, new growth',
      section: 'projects',
      angle: 205.6,
      radius: 105
    },
    {
      id: 'ururangi',
      name: 'Ururangi',
      meaning: 'Winds of change',
      section: 'contact',
      angle: 257,
      radius: 115
    },
    {
      id: 'hiwa-i-te-rangi',
      name: 'Hiwa-i-te-rangi',
      meaning: 'Dreams, aspirations',
      section: 'footer',
      angle: 308.4,
      radius: 90
    }
  ])

  const initializeConstellation = () => {
    if (isInitialized.value) return

    // Find constellation container
    const constellation = document.querySelector('.matariki-constellation')
    if (!constellation) {
      console.warn('Matariki constellation container not found')
      return
    }

    // Initialize star elements
    stars.value.forEach((star, index) => {
      const starElement = constellation.querySelector(`[data-star="${star.id}"]`)
      if (starElement) {
        star.element = starElement as HTMLElement
        
        // Set initial position
        const x = Math.cos((star.angle * Math.PI) / 180) * star.radius
        const y = Math.sin((star.angle * Math.PI) / 180) * star.radius
        
        gsap.set(starElement, {
          x: x,
          y: y,
          scale: 1,
          opacity: 0.6
        })
      }
    })

    setupScrollTriggers()
    isInitialized.value = true
  }

  const setupScrollTriggers = () => {
    // Main constellation rotation
    const constellationElement = document.querySelector('.matariki-constellation')
    if (!constellationElement) return

    // Create main timeline for constellation rotation
    animationTimeline.value = gsap.timeline({
      scrollTrigger: {
        trigger: '.home-page',
        start: 'top top',
        end: 'bottom bottom',
        scrub: 1,
        onUpdate: (self) => {
          // Rotate entire constellation based on scroll progress
          const rotation = self.progress * 360
          gsap.set(constellationElement, { rotation })
        }
      }
    })

    // Create scroll triggers for each section
    const sectionMapping = [
      { selector: '.hero-section', starSection: 'hero' },
      { selector: '.articles-preview-section', starSection: 'articles' },
      { selector: '.shortform-preview-section', starSection: 'shortform' },
      { selector: '.ai-help-preview-section', starSection: 'ai-help' },
      { selector: '.projects-preview-section', starSection: 'projects' },
      { selector: '.contact-preview-section', starSection: 'contact' }
    ]

    sectionMapping.forEach(({ selector, starSection }) => {
      const sectionElement = document.querySelector(selector)
      if (!sectionElement) return

      const correspondingStar = stars.value.find(star => star.section === starSection)
      if (!correspondingStar?.element) return

      ScrollTrigger.create({
        trigger: sectionElement,
        start: 'top 60%',
        end: 'bottom 40%',
        onEnter: () => activateStar(correspondingStar),
        onLeave: () => deactivateStar(correspondingStar),
        onEnterBack: () => activateStar(correspondingStar),
        onLeaveBack: () => deactivateStar(correspondingStar)
      })
    })
  }

  const activateStar = (star: MatarikiStar) => {
    if (!star.element) return

    currentActiveSection.value = star.section

    // Add active class to the star
    star.element.classList.add('active')

    // Remove active class from other stars
    stars.value.forEach(otherStar => {
      if (otherStar.id !== star.id && otherStar.element) {
        otherStar.element.classList.remove('active')
      }
    })

    // Trigger section reveal
    revealSection(star.section)

    // Trigger section background change
    updateSectionBackground(star.section)
  }

  const deactivateStar = (star: MatarikiStar) => {
    if (!star.element) return

    // Remove active class
    star.element.classList.remove('active')
  }

  const revealSection = (section: string) => {
    // Map section names to CSS classes
    const sectionClassMap: Record<string, string> = {
      'hero': 'hero-section',
      'articles': 'articles-preview-section',
      'shortform': 'shortform-preview-section',
      'ai-help': 'ai-help-preview-section',
      'projects': 'projects-preview-section',
      'contact': 'contact-preview-section'
    }

    const sectionClass = sectionClassMap[section]
    if (!sectionClass) return

    const sectionElement = document.querySelector(`.${sectionClass}`)
    if (!sectionElement) return

    // Remove skeleton class and add revealed class
    sectionElement.classList.remove('section-skeleton')
    sectionElement.classList.add('section-revealed')
  }

  const updateSectionBackground = (section: string) => {
    // Map section names to CSS classes
    const sectionClassMap: Record<string, string> = {
      'hero': 'hero-section',
      'articles': 'articles-preview-section',
      'shortform': 'shortform-preview-section',
      'ai-help': 'ai-help-preview-section',
      'projects': 'projects-preview-section',
      'contact': 'contact-preview-section',
      'footer': 'contact-preview-section' // Footer maps to contact for now
    }

    const sectionClass = sectionClassMap[section]
    if (!sectionClass) return

    const sectionElement = document.querySelector(`.${sectionClass}`)
    if (!sectionElement) return

    // Add active class for CSS transitions
    document.querySelectorAll('.hero-section, .articles-preview-section, .shortform-preview-section, .ai-help-preview-section, .projects-preview-section, .contact-preview-section').forEach(el => {
      el.classList.remove('section-active')
    })
    sectionElement.classList.add('section-active')
  }

  const cleanup = () => {
    if (animationTimeline.value) {
      animationTimeline.value.kill()
    }
    ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    isInitialized.value = false
  }

  onMounted(() => {
    // Initialize after a short delay to ensure DOM is ready
    setTimeout(initializeConstellation, 100)
  })

  onUnmounted(() => {
    cleanup()
  })

  return {
    stars,
    currentActiveSection,
    isInitialized,
    initializeConstellation,
    cleanup
  }
}
