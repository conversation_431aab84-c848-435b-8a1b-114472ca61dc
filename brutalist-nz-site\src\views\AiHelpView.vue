<template>
  <div class="ai-help-page">
    <div class="site-container">
      <header class="page-header">
        <h1>AI Help & Guides</h1>
        <p class="page-subtitle">
          Practical AI guidance without the hype. Learn to use AI tools effectively with honest ROI analysis and real-world applications.
        </p>
      </header>

      <section class="guides-section">
        <h2>Available Guides</h2>
        <div class="guides-grid">
          <article v-for="guide in guides" :key="guide.slug" class="guide-card">
            <div class="guide-content">
              <h3>
                <router-link :to="`/ai-help/${guide.slug}`" class="guide-link">
                  {{ guide.title }}
                </router-link>
              </h3>
              <p class="guide-description">
                {{ guide.description }}
              </p>
              <div class="guide-meta">
                <span class="guide-type">{{ guide.type }}</span>
                <span class="guide-length">{{ guide.readTime }}</span>
              </div>
              <div class="guide-downloads" v-if="guide.downloads?.length">
                <strong>Downloads:</strong>
                <a
                  v-for="download in guide.downloads"
                  :key="download.url"
                  :href="download.url"
                  target="_blank"
                  class="download-link"
                >
                  {{ download.icon }} {{ download.title }}
                </a>
              </div>
            </div>
          </article>
        </div>
      </section>

      <section class="workshops-section">
        <h2>Upcoming Workshops</h2>
        <div class="workshops-grid">
          <article class="workshop-card">
            <div class="workshop-content">
              <h3>AI Prompting Masterclass</h3>
              <p class="workshop-description">
                Hands-on workshop covering the C-S-T & P-I-T framework with real-world exercises
                and practical applications for business professionals.
              </p>
              <div class="workshop-meta">
                <span class="workshop-date">📅 Coming Soon</span>
                <span class="workshop-duration">⏱️ 2 hours</span>
                <span class="workshop-format">💻 Online</span>
              </div>
              <div class="workshop-actions">
                <button class="workshop-button" disabled>
                  Notify Me When Available
                </button>
              </div>
            </div>
          </article>

          <article class="workshop-card">
            <div class="workshop-content">
              <h3>AI Tools for Content Creation</h3>
              <p class="workshop-description">
                Practical workshop exploring AI tools for writing, editing, and content strategy
                with honest ROI analysis and real-world case studies.
              </p>
              <div class="workshop-meta">
                <span class="workshop-date">📅 Planning Phase</span>
                <span class="workshop-duration">⏱️ 90 minutes</span>
                <span class="workshop-format">💻 Online</span>
              </div>
              <div class="workshop-actions">
                <button class="workshop-button" disabled>
                  Notify Me When Available
                </button>
              </div>
            </div>
          </article>
        </div>
        <div class="workshops-note">
          <p>
            <strong>Note:</strong> Workshops are currently in development. Sign up for the
            <a href="#" target="_blank">Frame Check newsletter</a> to be notified when they become available.
          </p>
        </div>
      </section>

      <section class="about-section">
        <h2>About These Guides</h2>
        <p>
          These guides are part of the <strong>Frame Check</strong> approach to AI tools - focusing on practical applications,
          honest ROI analysis, and real-world results. No hype, just what works.
        </p>
        <p>
          Each guide incorporates New Zealand values of straightforward communication and practical wisdom,
          helping busy professionals make informed decisions about AI tool adoption.
        </p>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useGuides } from '@/composables/useGuides'

const { getGuides } = useGuides()
const guides = getGuides()
</script>

<style scoped>
.ai-help-page {
  padding: var(--space-8) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--space-12);
}

.page-subtitle {
  font-size: var(--text-lg);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: var(--space-4) auto 0;
  line-height: 1.6;
}

.guides-section,
.workshops-section {
  margin-bottom: var(--space-12);
}

.guides-section h2,
.workshops-section h2 {
  margin-bottom: var(--space-6);
  font-size: var(--text-2xl);
}

.guides-grid {
  display: grid;
  gap: var(--space-6);
}

.guide-card {
  border: 2px solid var(--color-border);
  padding: var(--space-6);
  background: var(--color-background);
}

.guide-card:hover {
  border-color: var(--color-primary);
}

.guide-content h3 {
  margin: 0 0 var(--space-3) 0;
  font-size: var(--text-xl);
}

.guide-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: bold;
}

.guide-link:hover {
  text-decoration: underline;
}

.guide-description {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
  line-height: 1.6;
}

.guide-meta {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
  font-size: var(--text-sm);
}

.guide-type {
  background: var(--color-primary);
  color: white;
  padding: var(--space-1) var(--space-2);
  font-weight: bold;
  text-transform: uppercase;
  font-size: var(--text-xs);
}

.guide-length {
  color: var(--color-text-secondary);
}

.guide-downloads {
  font-size: var(--text-sm);
}

.guide-downloads strong {
  display: block;
  margin-bottom: var(--space-2);
}

.download-link {
  display: inline-block;
  margin-right: var(--space-4);
  color: var(--color-primary);
  text-decoration: none;
  padding: var(--space-1) 0;
}

.download-link:hover {
  text-decoration: underline;
}

.about-section {
  background: var(--color-background-secondary, #f8f9fa);
  padding: var(--space-6);
  border-left: 4px solid var(--color-primary);
}

.about-section h2 {
  margin-top: 0;
  margin-bottom: var(--space-4);
}

.about-section p {
  line-height: 1.6;
  margin-bottom: var(--space-3);
}

.about-section p:last-child {
  margin-bottom: 0;
}

/* Workshop Styles */
.workshops-grid {
  display: grid;
  gap: var(--space-6);
  grid-template-columns: 1fr;
  margin-bottom: var(--space-6);
}

@media (min-width: 768px) {
  .workshops-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.workshop-card {
  border: 2px solid var(--color-border);
  padding: var(--space-6);
  background: var(--color-background);
  transition: all 0.2s ease;
}

.workshop-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.workshop-content h3 {
  margin: 0 0 var(--space-3) 0;
  font-size: var(--text-xl);
  color: var(--color-text);
}

.workshop-description {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
  line-height: 1.6;
}

.workshop-meta {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
  font-size: var(--text-sm);
}

@media (min-width: 480px) {
  .workshop-meta {
    flex-direction: row;
    gap: var(--space-4);
  }
}

.workshop-date,
.workshop-duration,
.workshop-format {
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.workshop-actions {
  margin-top: auto;
}

.workshop-button {
  background: var(--color-primary);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-4);
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: 100%;
}

.workshop-button:hover:not(:disabled) {
  background: var(--color-primary-dark, #2563eb);
}

.workshop-button:disabled {
  background: var(--color-text-secondary);
  cursor: not-allowed;
  opacity: 0.7;
}

.workshops-note {
  background: var(--color-background-secondary, #f8f9fa);
  padding: var(--space-4);
  border-left: 4px solid var(--color-primary);
  font-size: var(--text-sm);
}

.workshops-note p {
  margin: 0;
  line-height: 1.6;
}

.workshops-note a {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: bold;
}

.workshops-note a:hover {
  text-decoration: underline;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .workshop-card:hover {
    transform: none;
  }
}
</style>
