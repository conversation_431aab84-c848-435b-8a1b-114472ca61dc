<script setup lang="ts">
import { RouterView } from 'vue-router'
import AppHeader from './components/layout/AppHeader.vue'
import AppFooter from './components/layout/AppFooter.vue'
import MatarikiConstellation from './components/MatarikiConstellation.vue'
</script>

<template>
  <div class="app">
    <!-- Matariki constellation - persistent across all pages -->
    <MatarikiConstellation />

    <!-- Main site structure -->
    <AppHeader />

    <main class="main-content">
      <RouterView />
    </main>

    <AppFooter />
  </div>
</template>

<style scoped>
.app {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  position: relative;
  z-index: 1;
}
</style>
