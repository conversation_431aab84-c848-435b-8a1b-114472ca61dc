<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useSectionStates } from '../../composables/useSectionStates'

interface Props {
  sectionId: string
  starName: string
  themeTitle: string
  themeSubtitle: string
  mainTitle: string
  darkTheme?: boolean
  backgroundPattern?: string
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  darkTheme: false,
  backgroundPattern: 'default',
  className: ''
})

const emit = defineEmits<{
  stateChange: [state: string, progress: number]
  sectionActive: [sectionId: string]
}>()

// Section state management
const { currentState, progress, setupScrollTrigger, cleanup } = useSectionStates(props.sectionId)

// Computed classes for dynamic styling
const sectionClasses = computed(() => [
  'scroll-section',
  `section-${props.sectionId}`,
  `state-${currentState.value}`,
  `pattern-${props.backgroundPattern}`,
  {
    'dark-theme': props.darkTheme,
    'section-active': currentState.value === 'loaded'
  },
  props.className
])

// Watch for state changes and emit events
const handleStateChange = (state: string, progressValue: number) => {
  emit('stateChange', state, progressValue)
  if (state === 'loaded') {
    emit('sectionActive', props.sectionId)
  }
}

onMounted(() => {
  setupScrollTrigger(handleStateChange)
})

onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <section 
    :class="sectionClasses"
    :data-section="sectionId"
    :data-star="starName"
    :data-scroll-section="sectionId"
  >
    <!-- Theme Layer (always visible, transitions to background) -->
    <div class="theme-layer" data-gsap="theme-layer">
      <div class="theme-content">
        <h2 class="theme-title" data-gsap="theme-title">{{ themeTitle }}</h2>
        <p class="theme-subtitle" data-gsap="theme-subtitle">{{ themeSubtitle }}</p>
      </div>
    </div>

    <!-- Background Pattern Layer -->
    <div class="background-pattern" data-gsap="background-pattern">
      <div :class="`pattern-${backgroundPattern}`"></div>
    </div>

    <!-- Main Content Layer -->
    <div class="site-container">
      <div class="section-content" data-gsap="section-content">
        <!-- Skeleton State Elements -->
        <div class="skeleton-elements" data-gsap="skeleton-elements">
          <div class="skeleton-title"></div>
          <div class="skeleton-subtitle"></div>
          <div class="skeleton-content">
            <slot name="skeleton" />
          </div>
        </div>

        <!-- Main Content Elements -->
        <div class="main-content" data-gsap="main-content">
          <header class="section-header" data-gsap="section-header">
            <h2 class="section-title" data-gsap="section-title">{{ mainTitle }}</h2>
            <slot name="header" />
          </header>
          
          <div class="section-body" data-gsap="section-body">
            <slot name="content" />
          </div>

          <footer class="section-footer" data-gsap="section-footer">
            <slot name="footer" />
          </footer>
        </div>
      </div>
    </div>

    <!-- Debug Info (development only) -->
    <div v-if="$dev" class="debug-info">
      <small>{{ sectionId }} | {{ currentState }} | {{ Math.round(progress * 100) }}%</small>
    </div>
  </section>
</template>

<style scoped>
.scroll-section {
  position: relative;
  min-height: 100vh;
  padding: var(--space-12) 0;
  overflow: hidden;
  transition: all 0.8s ease;
}

/* Theme Layer - Always visible, transitions to background */
.theme-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  opacity: 1;
  transition: all 0.8s ease;
}

.theme-content {
  text-align: center;
  pointer-events: none;
}

.theme-title {
  font-size: var(--text-4xl);
  font-weight: 600;
  color: var(--color-kawakawa);
  margin-bottom: var(--space-2);
  opacity: 0.7;
  transition: all 0.6s ease;
}

.theme-subtitle {
  font-size: var(--text-lg);
  color: var(--color-text-secondary);
  opacity: 0.6;
  transition: all 0.6s ease;
}

/* Background Pattern Layer */
.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  opacity: 0;
  transition: all 1s ease;
}

/* Main Content Layer */
.section-content {
  position: relative;
  z-index: 3;
}

/* Skeleton Elements - Initial State */
.skeleton-elements {
  opacity: 1;
  transition: all 0.6s ease;
}

.skeleton-title {
  height: 3rem;
  background: linear-gradient(90deg, var(--color-mist) 25%, rgba(255,255,255,0.5) 50%, var(--color-mist) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 8px;
  margin-bottom: var(--space-4);
  max-width: 60%;
}

.skeleton-subtitle {
  height: 1.5rem;
  background: linear-gradient(90deg, var(--color-mist) 25%, rgba(255,255,255,0.5) 50%, var(--color-mist) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite 0.5s;
  border-radius: 6px;
  margin-bottom: var(--space-6);
  max-width: 40%;
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

/* Main Content - Hidden initially */
.main-content {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease;
}

.section-header {
  margin-bottom: var(--space-8);
}

.section-title {
  font-size: var(--text-3xl);
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

/* State-based styling */
.scroll-section.state-skeleton .theme-layer {
  opacity: 1;
}

.scroll-section.state-skeleton .skeleton-elements {
  opacity: 1;
}

.scroll-section.state-skeleton .main-content {
  opacity: 0;
  transform: translateY(50px);
}

.scroll-section.state-entering .theme-layer {
  opacity: 0.3;
}

.scroll-section.state-entering .theme-title,
.scroll-section.state-entering .theme-subtitle {
  transform: translateY(-20px);
  opacity: 0.3;
}

.scroll-section.state-entering .background-pattern {
  opacity: 0.5;
}

.scroll-section.state-entering .skeleton-elements {
  opacity: 0;
  transform: translateY(-30px);
}

.scroll-section.state-entering .main-content {
  opacity: 0.7;
  transform: translateY(20px);
}

.scroll-section.state-loaded .theme-layer {
  opacity: 0.1;
}

.scroll-section.state-loaded .background-pattern {
  opacity: 1;
}

.scroll-section.state-loaded .skeleton-elements {
  opacity: 0;
  transform: translateY(-50px);
}

.scroll-section.state-loaded .main-content {
  opacity: 1;
  transform: translateY(0);
}

.scroll-section.state-exiting .main-content {
  opacity: 0.8;
}

/* Dark theme variant */
.scroll-section.dark-theme {
  background: linear-gradient(135deg, var(--color-charcoal) 0%, #0a0a0a 100%);
  color: white;
}

.scroll-section.dark-theme .theme-title {
  color: rgba(255, 255, 255, 0.9);
}

.scroll-section.dark-theme .theme-subtitle {
  color: rgba(255, 255, 255, 0.7);
}

.scroll-section.dark-theme .section-title {
  color: white;
}

/* Shimmer animation for skeleton */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Debug info */
.debug-info {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 100;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .scroll-section {
    padding: var(--space-8) 0;
  }
  
  .theme-title {
    font-size: var(--text-2xl);
  }
  
  .section-title {
    font-size: var(--text-2xl);
  }
}
</style>
