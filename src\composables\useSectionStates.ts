import { ref, onUnmounted } from 'vue'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger)

export type SectionState = 'skeleton' | 'entering' | 'loaded' | 'exiting'

export interface SectionAnimationConfig {
  skeletonToEntering?: gsap.TweenVars
  enteringToLoaded?: gsap.TweenVars
  loadedToExiting?: gsap.TweenVars
  customAnimations?: {
    [key: string]: gsap.TweenVars
  }
}

export const useSectionStates = (sectionId: string, config?: SectionAnimationConfig) => {
  const currentState = ref<SectionState>('skeleton')
  const progress = ref(0)
  const scrollTriggerInstance = ref<ScrollTrigger | null>(null)
  const animationTimeline = ref<gsap.core.Timeline | null>(null)

  // Default animation configurations
  const defaultConfig: SectionAnimationConfig = {
    skeletonToEntering: {
      duration: 0.8,
      ease: "power2.out"
    },
    enteringToLoaded: {
      duration: 1.0,
      ease: "power2.inOut"
    },
    loadedToExiting: {
      duration: 0.6,
      ease: "power2.in"
    }
  }

  const animationConfig = { ...defaultConfig, ...config }

  const updateSectionState = (scrollProgress: number, callback?: (state: SectionState, progress: number) => void) => {
    let newState: SectionState = 'skeleton'
    
    if (scrollProgress <= 0.1) {
      newState = 'skeleton'
    } else if (scrollProgress <= 0.5) {
      newState = 'entering'
    } else if (scrollProgress <= 0.9) {
      newState = 'loaded'
    } else {
      newState = 'exiting'
    }

    if (newState !== currentState.value) {
      currentState.value = newState
      animateStateTransition(newState, scrollProgress)
      callback?.(newState, scrollProgress)
    }

    progress.value = scrollProgress
  }

  const animateStateTransition = (state: SectionState, scrollProgress: number) => {
    const sectionElement = document.querySelector(`[data-section="${sectionId}"]`)
    if (!sectionElement) return

    // Kill existing timeline
    if (animationTimeline.value) {
      animationTimeline.value.kill()
    }

    // Create new timeline for state transition
    animationTimeline.value = gsap.timeline()

    switch (state) {
      case 'skeleton':
        animateToSkeleton(sectionElement)
        break
      case 'entering':
        animateToEntering(sectionElement, scrollProgress)
        break
      case 'loaded':
        animateToLoaded(sectionElement, scrollProgress)
        break
      case 'exiting':
        animateToExiting(sectionElement, scrollProgress)
        break
    }
  }

  const animateToSkeleton = (element: Element) => {
    const tl = animationTimeline.value!
    
    tl.set(element.querySelector('.theme-layer'), { opacity: 1 })
      .set(element.querySelector('.skeleton-elements'), { opacity: 1, y: 0 })
      .set(element.querySelector('.main-content'), { opacity: 0, y: 50 })
      .set(element.querySelector('.background-pattern'), { opacity: 0 })
  }

  const animateToEntering = (element: Element, scrollProgress: number) => {
    const tl = animationTimeline.value!
    const enteringProgress = (scrollProgress - 0.1) / 0.4 // Normalize to 0-1 for entering phase
    
    tl.to(element.querySelector('.theme-layer'), {
        opacity: 0.3,
        ...animationConfig.skeletonToEntering
      })
      .to(element.querySelector('.theme-title'), {
        y: -20,
        opacity: 0.3,
        ...animationConfig.skeletonToEntering
      }, "<")
      .to(element.querySelector('.theme-subtitle'), {
        y: -20,
        opacity: 0.3,
        ...animationConfig.skeletonToEntering
      }, "<")
      .to(element.querySelector('.skeleton-elements'), {
        opacity: 0,
        y: -30,
        ...animationConfig.skeletonToEntering
      }, "<")
      .to(element.querySelector('.background-pattern'), {
        opacity: 0.5,
        ...animationConfig.skeletonToEntering
      }, "<")
      .to(element.querySelector('.main-content'), {
        opacity: 0.7,
        y: 20,
        ...animationConfig.skeletonToEntering
      }, "<")
  }

  const animateToLoaded = (element: Element, scrollProgress: number) => {
    const tl = animationTimeline.value!
    
    tl.to(element.querySelector('.theme-layer'), {
        opacity: 0.1,
        ...animationConfig.enteringToLoaded
      })
      .to(element.querySelector('.background-pattern'), {
        opacity: 1,
        ...animationConfig.enteringToLoaded
      }, "<")
      .to(element.querySelector('.skeleton-elements'), {
        opacity: 0,
        y: -50,
        ...animationConfig.enteringToLoaded
      }, "<")
      .to(element.querySelector('.main-content'), {
        opacity: 1,
        y: 0,
        ...animationConfig.enteringToLoaded
      }, "<")
      
    // Stagger animation for content elements
    const contentElements = element.querySelectorAll('[data-gsap*="section-"]')
    if (contentElements.length > 0) {
      tl.fromTo(contentElements, 
        { opacity: 0, y: 30 },
        { 
          opacity: 1, 
          y: 0, 
          stagger: 0.1,
          duration: 0.6,
          ease: "power2.out"
        }, 
        "-=0.4"
      )
    }
  }

  const animateToExiting = (element: Element, scrollProgress: number) => {
    const tl = animationTimeline.value!
    
    tl.to(element.querySelector('.main-content'), {
      opacity: 0.8,
      scale: 0.98,
      ...animationConfig.loadedToExiting
    })
  }

  const setupScrollTrigger = (callback?: (state: SectionState, progress: number) => void) => {
    const sectionElement = document.querySelector(`[data-section="${sectionId}"]`)
    if (!sectionElement) {
      console.warn(`Section element with data-section="${sectionId}" not found`)
      return
    }

    scrollTriggerInstance.value = ScrollTrigger.create({
      trigger: sectionElement,
      start: "top 80%",
      end: "bottom 20%",
      scrub: false,
      onUpdate: (self) => {
        updateSectionState(self.progress, callback)
      },
      onEnter: () => {
        updateSectionState(0.2, callback)
      },
      onLeave: () => {
        updateSectionState(0.95, callback)
      },
      onEnterBack: () => {
        updateSectionState(0.7, callback)
      },
      onLeaveBack: () => {
        updateSectionState(0.05, callback)
      }
    })
  }

  const cleanup = () => {
    if (scrollTriggerInstance.value) {
      scrollTriggerInstance.value.kill()
      scrollTriggerInstance.value = null
    }
    if (animationTimeline.value) {
      animationTimeline.value.kill()
      animationTimeline.value = null
    }
  }

  onUnmounted(() => {
    cleanup()
  })

  return {
    currentState,
    progress,
    setupScrollTrigger,
    cleanup,
    updateSectionState
  }
}
